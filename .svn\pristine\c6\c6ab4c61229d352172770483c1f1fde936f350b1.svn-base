/* ========================================
   UI ANIMATIONS.JS - Progressive disclosure and animations
   Handles section animations, progressive disclosure, and scroll management
   ======================================== */

// ========================================
// AUTO-SCROLL & PROGRESSIVE DISCLOSURE FUNCTIONS
// ========================================


// Hide section - Ẩn section
function hideSectionWithAnimation(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.remove('section-visible', 'slide-in', 'fade-in');
        element.classList.add('section-hidden');
        console.log(`🙈 Hidden section: ${elementId}`);
    }
}

// Progressive disclosure workflow - Workflow hiển thị từng bước (NO AUTO SCROLL)
function startProgressiveDisclosure() {
    console.log('🎬 Starting progressive disclosure workflow (NO AUTO SCROLL)...');

    // Step 1: Hiển thị results section KHÔNG scroll
    setTimeout(() => {
        showSectionWithAnimation('resultsSection', 'fade-in');
        console.log('📍 Step 1: Results section shown (NO SCROLL)');
    }, 1000);

    // Step 2: Hiển thị OCR results (Card Info) TRƯỚC KHÔNG scroll + FETCH DATA
    setTimeout(() => {
        showSectionWithAnimation('cardInfoSection', 'slide-in');
        console.log('📄 Step 2: OCR Card Info section shown FIRST (NO SCROLL)');

        // Show loading state first
        if (typeof showCardInfoLoading === 'function') {
            showCardInfoLoading();
        }

        // Fetch and display OCR results
                        if (typeof fetchOCRResults === 'function') {
                    fetchOCRResults(0, 10, false).then(ocrData => {
                if (ocrData) {
                    console.log('✅ OCR data loaded and displayed');
                }
            });
        }
    }, 2000);
}

// Show images section - Hiển thị section ảnh (NO AUTO SCROLL) + FETCH DATA
function showImagesSection() {
    console.log('🖼️ Step 3: Showing AI images section AFTER OCR (NO SCROLL)...');
    setTimeout(() => {
        showSectionWithAnimation('imagesSection', 'slide-in');
        console.log('🎨 AI Images section shown SECOND (NO SCROLL)');

        // Fetch and display generated images
        if (typeof fetchGeneratedImages === 'function') {
            fetchGeneratedImages().then(imageData => {
                if (imageData) {
                    console.log('✅ Generated images loaded and displayed');
                }
            });
        }
    }, 800); // Tăng delay để đảm bảo OCR đã hiển thị xong
}

// Show images section without fetching - Chỉ hiển thị section mà không fetch data
function showImagesSectionOnly() {
    console.log('🖼️ Showing AI images section (data already populated)...');

    // Debug: Kiểm tra section state trước khi hiển thị
    const section = document.getElementById('imagesSection');
    if (section) {
        console.log('🔍 Section state before show:');
        console.log('   - classList:', section.classList.toString());
        console.log('   - display:', window.getComputedStyle(section).display);
        console.log('   - opacity:', window.getComputedStyle(section).opacity);
        console.log('   - maxHeight:', window.getComputedStyle(section).maxHeight);
    }

    const success = showSectionWithAnimation('imagesSection', 'slide-in');

    // Debug: Kiểm tra section state sau khi hiển thị
    if (section && success) {
        setTimeout(() => {
            console.log('🔍 Section state after show:');
            console.log('   - classList:', section.classList.toString());
            console.log('   - display:', window.getComputedStyle(section).display);
            console.log('   - opacity:', window.getComputedStyle(section).opacity);
            console.log('   - maxHeight:', window.getComputedStyle(section).maxHeight);
        }, 100);
    }

    if (success) {
        console.log('✅ AI Images section displayed successfully');
    } else {
        console.error('❌ Failed to display AI Images section');
    }
    return success;
}

// Reset all sections - Reset tất cả sections về trạng thái ban đầu (NO AUTO SCROLL)
function resetAllSections() {
    console.log('🔄 Resetting all sections (NO SCROLL)...');
    hideSectionWithAnimation('resultsSection');
    hideSectionWithAnimation('cardInfoSection');
    hideSectionWithAnimation('imagesSection');

    // NO auto scroll - giữ nguyên vị trí hiện tại
    console.log('📍 Staying at current position (NO SCROLL)');
}

// ========================================
// ANIMATION UTILITIES
// ========================================

// Animate element with custom animation
function animateElement(elementId, animationName, duration = 300) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error(`❌ Element not found for animation: ${elementId}`);
        return Promise.reject(new Error('Element not found'));
    }

    return new Promise((resolve) => {
        element.style.animationDuration = `${duration}ms`;
        element.classList.add(animationName);

        const handleAnimationEnd = () => {
            element.classList.remove(animationName);
            element.removeEventListener('animationend', handleAnimationEnd);
            resolve();
        };

        element.addEventListener('animationend', handleAnimationEnd);
    });
}

// Fade in element
function fadeInElement(elementId, duration = 300) {
    return animateElement(elementId, 'fade-in', duration);
}

// Fade out element
function fadeOutElement(elementId, duration = 300) {
    return animateElement(elementId, 'fade-out', duration);
}

// Slide in element
function slideInElement(elementId, duration = 300) {
    return animateElement(elementId, 'slide-in', duration);
}

// Shake element (for errors)
function shakeElement(elementId, duration = 500) {
    return animateElement(elementId, 'shake', duration);
}

// ========================================
// SCROLL MANAGEMENT
// ========================================

// Enable/disable page scrolling
function setPageScrolling(enabled) {
    if (enabled) {
        document.body.classList.remove('no-scroll');
        document.body.classList.add('allow-scroll');
        console.log('📍 Page scrolling enabled');
    } else {
        document.body.classList.add('no-scroll');
        document.body.classList.remove('allow-scroll');
        console.log('📍 Page scrolling disabled');
    }
}



// Smooth scroll to top
function scrollToTop(duration = 500) {
    const start = window.pageYOffset;
    const startTime = performance.now();

    function scrollAnimation(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeInOutCubic = progress < 0.5
            ? 4 * progress * progress * progress
            : (progress - 1) * (2 * progress - 2) * (2 * progress - 2) + 1;

        window.scrollTo(0, start * (1 - easeInOutCubic));

        if (progress < 1) {
            requestAnimationFrame(scrollAnimation);
        }
    }

    requestAnimationFrame(scrollAnimation);
}

// Check if element is in viewport
function isElementInViewport(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return false;

    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// ========================================
// LOADING ANIMATIONS
// ========================================

// Show loading spinner on element
function showLoadingSpinner(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        spinner.id = `${elementId}-spinner`;
        element.appendChild(spinner);
        console.log(`⏳ Loading spinner shown on: ${elementId}`);
    }
}

// Hide loading spinner
function hideLoadingSpinner(elementId) {
    const spinner = document.getElementById(`${elementId}-spinner`);
    if (spinner) {
        spinner.remove();
        console.log(`✅ Loading spinner hidden from: ${elementId}`);
    }
}

// ========================================
// TRANSITION EFFECTS
// ========================================

// Cross-fade between two elements
function crossFade(fromElementId, toElementId, duration = 300) {
    const fromElement = document.getElementById(fromElementId);
    const toElement = document.getElementById(toElementId);

    if (!fromElement || !toElement) {
        console.error('❌ Elements not found for cross-fade');
        return Promise.reject(new Error('Elements not found'));
    }

    return new Promise((resolve) => {
        // Start fade out of first element
        fromElement.style.transition = `opacity ${duration}ms ease`;
        fromElement.style.opacity = '0';

        // Start fade in of second element after half duration
        setTimeout(() => {
            toElement.style.transition = `opacity ${duration}ms ease`;
            toElement.style.opacity = '1';

            setTimeout(() => {
                resolve();
            }, duration);
        }, duration / 2);
    });
}

// ========================================
// HELPER FUNCTION TO SHOW CARD INFO SECTION
// ========================================

// Helper function to show card info section
function showCardInfoSection() {
    console.log('📄 Showing card info section...');
    // This function is called after OCR data is populated
    // The section should already be visible from progressive disclosure
    // Just ensure it's visible
    const section = document.getElementById('cardInfoSection');
    if (section && !section.classList.contains('section-visible')) {
        showSectionWithAnimation('cardInfoSection', 'slide-in');
    }
}

// Export functions to global scope
window.autoScrollToElement = autoScrollToElement;
window.showSectionWithAnimation = showSectionWithAnimation;
window.hideSectionWithAnimation = hideSectionWithAnimation;
window.startProgressiveDisclosure = startProgressiveDisclosure;
window.showImagesSection = showImagesSection;
window.showImagesSectionOnly = showImagesSectionOnly;
window.resetAllSections = resetAllSections;
window.animateElement = animateElement;
window.fadeInElement = fadeInElement;
window.fadeOutElement = fadeOutElement;
window.slideInElement = slideInElement;
window.shakeElement = shakeElement;
window.setPageScrolling = setPageScrolling;
window.scrollToTop = scrollToTop;
window.isElementInViewport = isElementInViewport;
window.showLoadingSpinner = showLoadingSpinner;
window.hideLoadingSpinner = hideLoadingSpinner;
window.showCardInfoSection = showCardInfoSection;
