
from datetime import datetime
from database.connection import db

class CustomerHistory(db.Model):
    __tablename__ = 'TBL_CUSTOMER_HIS'
    
    history_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    customer_id = db.Column(db.Integer, db.<PERSON>ey('TBL_CUSTOMER.customer_id'), nullable=True, comment='ID của khách hàng')
    company_name = db.Column(db.Text, nullable=True, comment='Tên công ty')
    customer_addr = db.Column(db.Text, nullable=True, comment='Địa chỉ')
    customer_name = db.Column(db.Text, nullable=True, comment='Họ tên đầy đủ')
    customer_title = db.Column(db.Text, nullable=True, comment='Chức vụ')
    customer_email = db.Column(db.Text, nullable=True, comment='Email')
    customer_tel = db.Column(db.Text, nullable=True, comment='<PERSON><PERSON> điện thoại')
    customer_infor = db.Column(db.Text, nullable=True, comment='Toàn bộ các thông tin khác')
    history_time = db.Column(db.DateTime, default=datetime.now, comment='Thời gian')
    
    customer = db.relationship('Customer', backref=db.backref('histories', lazy=True))
    
    def to_dict(self):
        return {
            'history_id': self.history_id,
            'customer_id': self.customer_id,
            'company_name': self.company_name,
            'customer_addr': self.customer_addr,
            'customer_name': self.customer_name,
            'customer_title': self.customer_title,
            'customer_email': self.customer_email,
            'customer_tel': self.customer_tel,
            'customer_infor': self.customer_infor,
            'history_time': self.history_time.isoformat() if self.history_time else None
        }
    
    def __repr__(self):
        return f'<CustomerHistory {self.history_id} - {self.customer_name}>'
