
from database.connection import db

def upgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('''
            CREATE TABLE IF NOT EXISTS TBL_CUSTOMER_HIS (
                history_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                company_name TEXT,
                customer_addr TEXT,
                customer_name TEXT,
                customer_title TEXT,
                customer_email TEXT,
                customer_tel TEXT,
                customer_infor TEXT,
                history_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES TBL_CUSTOMER (customer_id)
            )
        '''))
        conn.commit()


def downgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('DROP TABLE IF EXISTS TBL_CUSTOMER_HIS'))
        conn.commit()
