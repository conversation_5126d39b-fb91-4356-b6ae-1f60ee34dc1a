/**
 * WebRTC Camera Solution for Client-Side Camera Access
 * Allows client to use their own camera instead of server camera
 */

class WebRTCCamera {
    constructor() {
        this.streams = {};
        this.videoElements = {};
        this.canvasElements = {};
        this.isInitialized = false;
    }

    async initialize() {
        try {
            console.log('🎥 Initializing WebRTC cameras...');

            // Get all available video devices
            const devices = await navigator.mediaDevices.enumerateDevices();
            const videoDevices = devices.filter(device => device.kind === 'videoinput');

            console.log(`📹 Found ${videoDevices.length} video devices`);

            if (videoDevices.length === 0) {
                throw new Error('No video devices found');
            }

            // Initialize first camera (Business Card)
            const constraints1 = {
                video: {
                    deviceId: videoDevices[0].deviceId,
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    frameRate: { ideal: 30 }
                },
                audio: false
            };
            await this.initializeCamera(0, constraints1);

            // Initialize second camera if available (Face)
            if (videoDevices.length > 1) {
                const constraints2 = {
                    video: {
                        deviceId: videoDevices[1].deviceId,
                        width: { ideal: 640 },
                        height: { ideal: 480 },
                        frameRate: { ideal: 30 }
                    },
                    audio: false
                };
                await this.initializeCamera(1, constraints2);
            } else {
                // Use same camera for both if only one available
                await this.initializeCamera(1, constraints1);
            }

            this.isInitialized = true;
            console.log('✅ WebRTC cameras initialized successfully');

        } catch (error) {
            console.error('❌ WebRTC initialization failed:', error);
            this.handleCameraError(error);
            throw error;
        }
    }

    async initializeCamera(cameraId, constraints) {
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.streams[cameraId] = stream;

            // Create video element for preview
            const videoElement = document.getElementById(`cam${cameraId}-webrtc`);
            const imgElement = document.getElementById(`cam${cameraId}`);

            if (videoElement) {
                videoElement.srcObject = stream;
                videoElement.play();
                this.videoElements[cameraId] = videoElement;

                // Hide server camera img, show WebRTC video
                if (imgElement) imgElement.style.display = 'none';
                videoElement.style.display = 'block';

                console.log(`✅ Camera ${cameraId} WebRTC stream started`);
            }

            // Create canvas for capture
            const canvas = document.createElement('canvas');
            canvas.width = 640;
            canvas.height = 480;
            this.canvasElements[cameraId] = canvas;

        } catch (error) {
            console.error(`❌ Failed to initialize camera ${cameraId}:`, error);
            throw error;
        }
    }

    captureImage(cameraId) {
        return new Promise((resolve, reject) => {
            try {
                const video = this.videoElements[cameraId];
                const canvas = this.canvasElements[cameraId];
                
                if (!video || !canvas) {
                    throw new Error(`Camera ${cameraId} not initialized`);
                }

                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                
                // Convert to blob
                canvas.toBlob((blob) => {
                    if (blob) {
                        console.log(`📸 Captured image from camera ${cameraId}`);
                        resolve(blob);
                    } else {
                        reject(new Error('Failed to create image blob'));
                    }
                }, 'image/jpeg', 0.9);

            } catch (error) {
                console.error(`❌ Capture failed for camera ${cameraId}:`, error);
                reject(error);
            }
        });
    }

    async uploadCapturedImage(cameraId, blob) {
        try {
            const formData = new FormData();
            formData.append('image', blob, `camera_${cameraId}_${Date.now()}.jpg`);
            formData.append('camera_id', cameraId);

            const response = await fetch('/upload_camera_image', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                console.log(`✅ Image uploaded for camera ${cameraId}:`, result.image_path);
                return result;
            } else {
                throw new Error(result.message || 'Upload failed');
            }

        } catch (error) {
            console.error(`❌ Upload failed for camera ${cameraId}:`, error);
            throw error;
        }
    }

    async captureAndUpload(cameraId) {
        try {
            const blob = await this.captureImage(cameraId);
            const result = await this.uploadCapturedImage(cameraId, blob);
            return result;
        } catch (error) {
            console.error(`❌ Capture and upload failed for camera ${cameraId}:`, error);
            throw error;
        }
    }

    handleCameraError(error) {
        // Chỉ log error, không hiển thị cảnh báo cho user
        console.log('⚠️ WebRTC camera not available, using server camera fallback');

        // Không hiển thị error message để tránh làm phiền user
        // Ứng dụng sẽ tự động fallback về server camera
    }

    cleanup() {
        // Stop all streams
        Object.values(this.streams).forEach(stream => {
            stream.getTracks().forEach(track => track.stop());
        });
        
        this.streams = {};
        this.videoElements = {};
        this.canvasElements = {};
        this.isInitialized = false;
        
        console.log('🧹 WebRTC cameras cleaned up');
    }
}

// Global WebRTC camera instance
let webrtcCamera = null;

// Enhanced initialization with server environment detection
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🎥 Initializing camera system...');

    // Check if WebRTC is supported
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log('⚠️ WebRTC not supported, will use server cameras');
        return;
    }

    // Check HTTPS requirement for production
    if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        console.log('⚠️ WebRTC requires HTTPS in production. Checking server camera status...');

        // Check if server cameras are available
        try {
            const response = await fetch('/camera_status');
            const status = await response.json();

            if (status.webrtc_only_mode || (!status.camera0_available && !status.camera1_available)) {
                console.log('🌐 Server is in WebRTC-only mode but HTTPS required');
                showWebRTCRequiredMessage();
                return;
            }
        } catch (error) {
            console.log('⚠️ Cannot check server camera status');
        }
        return;
    }

    try {
        // Check server mode first
        const response = await fetch('/camera_status');
        const serverStatus = await response.json();

        if (serverStatus.webrtc_only_mode) {
            console.log('🌐 Server is in WebRTC-only mode - Client cameras required');
        }

        webrtcCamera = new WebRTCCamera();
        await webrtcCamera.initialize();

        // Make it globally accessible
        window.webrtcCamera = webrtcCamera;

        console.log('✅ WebRTC cameras ready for client-side capture');

        // Show success message if server is WebRTC-only
        if (serverStatus.webrtc_only_mode) {
            showWebRTCSuccessMessage();
        }

    } catch (error) {
        console.log('⚠️ WebRTC failed, checking server fallback:', error);

        // Check if server cameras are available as fallback
        try {
            const response = await fetch('/camera_status');
            const status = await response.json();

            if (status.webrtc_only_mode) {
                console.error('❌ WebRTC failed and server has no cameras - Cannot proceed');
                showWebRTCFailedMessage();
            } else {
                console.log('✅ Falling back to server cameras');
            }
        } catch (statusError) {
            console.log('⚠️ Cannot check server camera status for fallback');
        }

        webrtcCamera = null;
        window.webrtcCamera = null;
    }
});

// Safe WebRTC capture function
window.captureImageWebRTC = async function(cameraId) {
    try {
        // Safety check
        if (!webrtcCamera || !webrtcCamera.isInitialized) {
            throw new Error('WebRTC camera not initialized');
        }

        console.log(`📸 WebRTC capture for camera ${cameraId}`);
        const result = await webrtcCamera.captureAndUpload(cameraId);
        return result;
    } catch (error) {
        console.error('❌ WebRTC capture failed:', error);
        throw error;
    }
};

// User feedback functions
function showWebRTCRequiredMessage() {
    const message = document.createElement('div');
    message.style.cssText = `
        position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
        background: #ff6b6b; color: white; padding: 15px 25px;
        border-radius: 8px; z-index: 10000; font-weight: bold;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    message.innerHTML = '🔒 HTTPS required for camera access. Please use HTTPS URL.';
    document.body.appendChild(message);

    setTimeout(() => message.remove(), 10000);
}

function showWebRTCSuccessMessage() {
    const message = document.createElement('div');
    message.style.cssText = `
        position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
        background: #43e97b; color: white; padding: 15px 25px;
        border-radius: 8px; z-index: 10000; font-weight: bold;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    message.innerHTML = '📱 Using your device cameras successfully!';
    document.body.appendChild(message);

    setTimeout(() => message.remove(), 5000);
}

function showWebRTCFailedMessage() {
    const message = document.createElement('div');
    message.style.cssText = `
        position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
        background: #ff6b6b; color: white; padding: 15px 25px;
        border-radius: 8px; z-index: 10000; font-weight: bold;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    message.innerHTML = '❌ Camera access failed. Please allow camera permissions and refresh.';
    document.body.appendChild(message);

    setTimeout(() => message.remove(), 10000);
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (webrtcCamera) {
        webrtcCamera.cleanup();
    }
});
