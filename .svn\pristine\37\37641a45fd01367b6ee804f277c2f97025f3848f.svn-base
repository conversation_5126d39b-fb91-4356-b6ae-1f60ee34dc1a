/* ========================================
   WORKFLOW MANAGER.JS - Status management and progress tracking
   Manages workflow states, progress tracking, and status coordination
   ======================================== */

// GLOBAL VARIABLES

// Single Page Layout Management - Quản lý layout trang đơn
let currentStep = 1; // Bước hiện tại trong workflow

// STATUS MANAGEMENT FUNCTIONS

// Update section status indicators - Cập nhật chỉ báo trạng thái section
function updateSectionStatus(section, status, text) {
    // Lấy element chỉ báo trạng thái
    const statusIndicator = document.getElementById(`${section}Status`);
    // Lấy element text trạng thái
    const statusText = document.getElementById(`${section}StatusText`);

    // Cập nhật class cho chỉ báo (thay đổi màu sắc)
    if (statusIndicator) {
        statusIndicator.className = `status-indicator status-${status}`;
    }
    // Cập nhật text trạng thái
    if (statusText) {
        statusText.textContent = text;
    }
}

// Update workflow progress - Cập nhật tiến trình workflow
function updateWorkflowProgress(step, status = 'processing') {
    // Cập nhật bước hiện tại
    currentStep = step;

    // Reset tất cả trạng thái về pending
    updateSectionStatus('camera', 'pending', 'Sẵn sàng');
    updateSectionStatus('ai', 'pending', 'Chờ ảnh');
    updateSectionStatus('results', 'pending', 'Chờ xử lý');

    // Cập nhật trạng thái dựa trên bước hiện tại
    switch(step) {
        case 1: // Bước chụp ảnh
            updateSectionStatus('camera', status, status === 'processing' ? 'Đang chụp...' : 'Sẵn sàng');
            break;
        case 2: // Bước tạo AI
            updateSectionStatus('camera', 'complete', 'Đã chụp');
            updateSectionStatus('ai', status, status === 'processing' ? 'Đang tạo AI...' : 'Sẵn sàng tạo');
            break;
        case 3: // Bước hiển thị kết quả
            updateSectionStatus('camera', 'complete', 'Đã chụp');
            updateSectionStatus('ai', 'complete', 'Đã tạo AI');
            updateSectionStatus('results', status, status === 'complete' ? 'Hoàn thành' : 'Đang xử lý');
            break;
    }
}

// ========================================
// WORKFLOW STATUS TRACKING
// ========================================

// Track complete workflow status
function trackWorkflowStatus() {
    console.log('📊 === WORKFLOW STATUS TRACKING ===');

    // Check session status
    fetch('/api/session_status')
        .then(response => response.json())
        .then(data => {
            console.log('📊 Session Status:', data);

            if (data.status === 'success' && data.session) {
                const session = data.session;
                console.log(`📊 Session ID: ${session.session_id}`);
                console.log(`📊 Card Info Available: ${!!session.card_info && Object.keys(session.card_info).length > 0}`);
                console.log(`📊 Generated Images Count: ${session.generated_images ? session.generated_images.length : 0}`);

                // If we have card info, try to fetch and display it
                if (session.card_info && Object.keys(session.card_info).length > 0) {
                    console.log('📊 Card info found in session, updating display...');
                    if (typeof updateCardInfoDisplay === 'function') {
                        updateCardInfoDisplay(session.card_info);
                    }
                }

                // If we have generated images, try to fetch and display them
                if (session.generated_images && session.generated_images.length > 0) {
                    console.log('📊 Generated images found in session, updating display...');
                    if (typeof updateGeneratedImagesDisplay === 'function') {
                        updateGeneratedImagesDisplay({
                            status: 'success',
                            images: session.generated_images,
                            count: session.generated_images.length
                        });
                    }
                }
            } else {
                console.log('📊 No active session or session data');
            }
        })
        .catch(error => {
            console.error('📊 Error tracking workflow status:', error);
        });
}

// Monitor processing status
function monitorProcessingStatus() {
    console.log('📊 Starting processing status monitor...');

    const checkStatus = () => {
        fetch('/api/session_status')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.session) {
                    const session = data.session;

                    // Check if processing is complete
                    if (session.card_info && Object.keys(session.card_info).length > 0) {
                        console.log('📊 OCR processing detected as complete');
                        if (typeof fetchOCRResults === 'function') {
                            // Skip audio playback in checkStatus to avoid duplicates
                            fetchOCRResults(0, 10, true);
                        }
                    }

                    if (session.generated_images && session.generated_images.length > 0) {
                        console.log('📊 AI generation detected as complete');
                        if (typeof fetchGeneratedImages === 'function') {
                            fetchGeneratedImages();
                        }
                    }
                }
            })
            .catch(error => {
                console.error('📊 Error monitoring status:', error);
            });
    };

    // Check every 3 seconds for 60 seconds
    const interval = setInterval(checkStatus, 3000);
    setTimeout(() => {
        clearInterval(interval);
        console.log('📊 Processing status monitor stopped');
    }, 60000);
}

// Auto processing monitor - Triggered after face capture
function startAutoProcessingMonitor() {
    console.log('🚀 Starting auto processing monitor after face capture...');
    console.log('🔧 DEBUG: startAutoProcessingMonitor called - this should appear in console');

    let ocrFetched = false;
    let imagesFetched = false;
    let audioPlayed = false; // Flag to prevent duplicate audio playback

    const checkAutoStatus = () => {
        fetch('/api/session_status')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.session) {
                    const session = data.session;

                    // Check for OCR completion and fetch results
                    if (session.card_info && Object.keys(session.card_info).length > 0 && !ocrFetched) {
                        console.log('📊 Auto-monitor: OCR processing detected as complete');
                        if (typeof fetchOCRResults === 'function') {
                            // Skip audio playback if already played to avoid duplicates
                            const skipAudio = audioPlayed;
                            if (!audioPlayed && session.tts_audio_path) {
                                console.log('🔊 Auto-monitor: Will play TTS audio for the first time');
                                audioPlayed = true;
                            }
                            fetchOCRResults(0, 10, skipAudio);
                            ocrFetched = true;
                        }
                    }

                    // Check for AI generation completion and fetch results
                    if (session.generated_images && session.generated_images.length > 0 && !imagesFetched) {
                        console.log('📊 Auto-monitor: AI generation detected as complete');
                        console.log('🖼️ SOLUTION 1: Using images from session data to avoid race condition');
                        console.log('🔧 DEBUG: Found generated_images in session:', session.generated_images);

                        // SOLUTION 1: Use session data directly instead of API call to avoid race condition
                        if (typeof updateGeneratedImagesDisplay === 'function') {
                            const imageData = {
                                status: 'success',
                                images: session.generated_images,
                                count: session.generated_images.length
                            };
                            console.log('🔧 DEBUG: Calling updateGeneratedImagesDisplay with:', imageData);
                            updateGeneratedImagesDisplay(imageData);

                            // Show images section
                            if (typeof showSectionWithAnimation === 'function') {
                                showSectionWithAnimation('resultsSection', 'fade-in');
                                showSectionWithAnimation('imagesSection', 'slide-in');
                            }

                            imagesFetched = true;
                            console.log('✅ Images displayed directly from session data');
                        }
                    }

                    // Stop monitoring when both are complete
                    if (ocrFetched && imagesFetched) {
                        clearInterval(autoInterval);
                        console.log('✅ Auto processing monitor completed - both OCR and AI done');

                        // Call displayWorkflowResults with combined data
                        console.log('🎯 Calling displayWorkflowResults with combined data...');
                        if (typeof displayWorkflowResults === 'function') {
                            displayWorkflowResults({
                                status: 'success',
                                card_info: session.card_info || {},
                                generated_images: session.generated_images || [],
                                workflow_completed: true
                            });
                            console.log('✅ displayWorkflowResults called from auto-monitor');
                        } else {
                            console.error('❌ displayWorkflowResults function not found in auto-monitor');
                        }
                    }
                }
            })
            .catch(error => {
                console.error('📊 Auto monitoring error:', error);
            });
    };

    // Start monitoring with 1-second intervals
    const autoInterval = setInterval(checkAutoStatus, 1000);

    // Safety timeout - stop after 60 seconds
    setTimeout(() => {
        clearInterval(autoInterval);
        console.log('⏰ Auto processing monitor stopped after timeout');
    }, 60000);
}

// ========================================
// PROGRESS BAR MANAGEMENT
// ========================================

// Show progress bar
function showProgressBar() {
    const progressBar = document.getElementById('progressBar');
    const progressFill = document.getElementById('progressFill');
    
    if (progressBar) {
        progressBar.style.display = 'block';
        if (progressFill) {
            progressFill.style.width = '0%';
        }
    }
}

// Update progress bar
function updateProgressBar(percentage) {
    const progressFill = document.getElementById('progressFill');
    if (progressFill) {
        progressFill.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
    }
}

// Hide progress bar
function hideProgressBar() {
    const progressBar = document.getElementById('progressBar');
    if (progressBar) {
        progressBar.style.display = 'none';
    }
}

// ========================================
// WORKFLOW STATE MANAGEMENT
// ========================================

// Get current workflow state
function getCurrentWorkflowState() {
    return {
        currentStep,
        timestamp: Date.now(),
        sessionActive: true
    };
}

// Set workflow state
function setWorkflowState(state) {
    if (state && typeof state === 'object') {
        currentStep = state.currentStep || 1;
        console.log(`📊 Workflow state updated: Step ${currentStep}`);
    }
}

// Reset workflow to initial state
function resetWorkflowState() {
    currentStep = 1;
    updateWorkflowProgress(1, 'pending');
    hideProgressBar();
    console.log('📊 Workflow state reset to initial');
}

// ========================================
// WORKFLOW VALIDATION
// ========================================

// Validate workflow prerequisites
function validateWorkflowPrerequisites() {
    const errors = [];
    
    // Check if prompt is selected
    const promptSelect = document.getElementById('promptSelect');
    if (!promptSelect || !promptSelect.value) {
        errors.push('Vui lòng chọn style ảnh');
    }
    
    // Check if camera is available
    const cameraStream = document.querySelector('.camera-stream');
    if (!cameraStream || !cameraStream.srcObject) {
        errors.push('Camera chưa sẵn sàng');
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

// ========================================
// WORKFLOW EVENTS
// ========================================

// Workflow event listeners
const workflowEventListeners = new Map();

// Add workflow event listener
function addWorkflowEventListener(event, callback) {
    if (!workflowEventListeners.has(event)) {
        workflowEventListeners.set(event, []);
    }
    workflowEventListeners.get(event).push(callback);
}

// Remove workflow event listener
function removeWorkflowEventListener(event, callback) {
    if (workflowEventListeners.has(event)) {
        const listeners = workflowEventListeners.get(event);
        const index = listeners.indexOf(callback);
        if (index > -1) {
            listeners.splice(index, 1);
        }
    }
}

// Emit workflow event
function emitWorkflowEvent(event, data = null) {
    console.log(`📊 Workflow event: ${event}`, data);
    
    if (workflowEventListeners.has(event)) {
        workflowEventListeners.get(event).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`❌ Error in workflow event listener for ${event}:`, error);
            }
        });
    }
}

// ========================================
// WORKFLOW METRICS
// ========================================

// Track workflow metrics
const workflowMetrics = {
    startTime: null,
    endTime: null,
    steps: [],
    errors: []
};

// Start workflow timing
function startWorkflowTiming() {
    workflowMetrics.startTime = Date.now();
    workflowMetrics.steps = [];
    workflowMetrics.errors = [];
    console.log('📊 Workflow timing started');
}

// Add workflow step timing
function addWorkflowStepTiming(stepName, duration) {
    workflowMetrics.steps.push({
        name: stepName,
        duration,
        timestamp: Date.now()
    });
    console.log(`📊 Step "${stepName}" completed in ${duration}ms`);
}

// End workflow timing
function endWorkflowTiming() {
    workflowMetrics.endTime = Date.now();
    const totalDuration = workflowMetrics.endTime - workflowMetrics.startTime;
    console.log(`📊 Total workflow duration: ${totalDuration}ms`);
    return workflowMetrics;
}

// Export functions to global scope
window.updateSectionStatus = updateSectionStatus;
window.updateWorkflowProgress = updateWorkflowProgress;
window.trackWorkflowStatus = trackWorkflowStatus;
window.monitorProcessingStatus = monitorProcessingStatus;
window.startAutoProcessingMonitor = startAutoProcessingMonitor;
window.showProgressBar = showProgressBar;
window.updateProgressBar = updateProgressBar;
window.hideProgressBar = hideProgressBar;
window.getCurrentWorkflowState = getCurrentWorkflowState;
window.setWorkflowState = setWorkflowState;
window.resetWorkflowState = resetWorkflowState;
window.validateWorkflowPrerequisites = validateWorkflowPrerequisites;
window.addWorkflowEventListener = addWorkflowEventListener;
window.emitWorkflowEvent = emitWorkflowEvent;
