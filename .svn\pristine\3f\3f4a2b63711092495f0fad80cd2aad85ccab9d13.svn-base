from typing import List, Optional
from database.models.customer import Customer
from .base import BaseCRUD
from .exceptions import ValidationError

class CustomerCRUD(BaseCRUD):

    def __init__(self):
        super().__init__(Customer)

    def create_customer(self, company_name: str, customer_name: str,
                       customer_email: Optional[str] = None, **kwargs) -> Customer:
        return self.create(
            company_name=company_name,
            customer_name=customer_name,
            customer_email=customer_email,
            **kwargs
        )

    def get_by_email(self, email: str) -> Optional[Customer]:
        return Customer.query.filter_by(customer_email=email).first()

    def get_by_name(self, name: str) -> List[Customer]:
        return Customer.query.filter(Customer.customer_name.like(f'%{name}%')).all()

    def get_by_company(self, company_name: str) -> List[Customer]:
        return Customer.query.filter(Customer.company_name.like(f'%{company_name}%')).all()

    def search(self, keyword: str) -> List[Customer]:
        return Customer.query.filter(
            (Customer.customer_name.like(f'%{keyword}%')) |
            (Customer.company_name.like(f'%{keyword}%')) |
            (Customer.customer_email.like(f'%{keyword}%'))
        ).all()

    def get_with_checkins(self, customer_id: int) -> Customer:
        from sqlalchemy.orm import joinedload
        return Customer.query.options(joinedload(Customer.checkins)).get(customer_id)

    def get_with_histories(self, customer_id: int) -> Customer:
        from sqlalchemy.orm import joinedload
        return Customer.query.options(joinedload(Customer.histories)).get(customer_id)

    def update_customer(self, customer_id: int, **kwargs) -> Customer:
        return self.update(customer_id, **kwargs)

    def delete_customer(self, customer_id: int) -> bool:
        customer = self.get_by_id_or_404(customer_id)
        if customer.checkins or customer.histories:
            raise ValidationError("Không thể xóa khách hàng có checkin hoặc lịch sử")
        return self.delete(customer_id)
