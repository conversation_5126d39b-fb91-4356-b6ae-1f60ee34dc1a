/* ========================================
   UTILS.JS - Utility functions and helpers
   Common utility functions used across the application
   ======================================== */

// ========================================
// UTILITY FUNCTIONS
// ========================================

// Show toast notification
function showMessage(message, type = 'info') {
    console.log(`📢 showMessage called: ${message} (${type})`);

    // Create toast container if not exists
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    const toastId = 'toast_' + Date.now();
    toast.id = toastId;

    const bgColor = type === 'success' ? '#4CAF50' :
                   type === 'error' ? '#f44336' :
                   type === 'warning' ? '#ff9800' :
                   '#2196F3';

    toast.style.cssText = `
        background: ${bgColor};
        color: white;
        padding: 12px 20px;
        margin-bottom: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
        word-wrap: break-word;
        pointer-events: auto;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    toast.textContent = message;

    // Add to container
    toastContainer.appendChild(toast);
    console.log(`📢 Toast created: ${message}`);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 10);

    // Auto remove after 4 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
                console.log(`📢 Toast removed: ${message}`);
            }
        }, 300);
    }, 4000);

    console.log(`📢 ${type.toUpperCase()}: ${message}`);
}

// Download image - Tải ảnh về máy (function dự phòng)
function downloadImage(imagePath, filename) {
    const link = document.createElement('a'); // Tạo element link ẩn
    link.href = '/' + imagePath;              // Đặt đường dẫn ảnh
    link.download = filename;                 // Đặt tên file download
    document.body.appendChild(link);          // Thêm vào DOM
    link.click();                            // Trigger click để download
    document.body.removeChild(link);         // Xóa khỏi DOM
}

// Play TTS Audio with notification on browser
function playTTSAudio(audioPath) {
    console.log('🔊 playTTSAudio called with path:', audioPath);

    if (!audioPath) {
        console.log('⚠️ No audio path provided');
        return;
    }

    try {
        console.log('🔊 Creating audio element...');

        // Ensure proper audio path format
        let fullAudioPath = audioPath;
        if (!audioPath.startsWith('http') && !audioPath.startsWith('/')) {
            fullAudioPath = '/' + audioPath;
        }
        console.log('🔊 Full audio path:', fullAudioPath);

        // Create audio element for browser playback
        const audio = new Audio(fullAudioPath);
        audio.volume = 0.8; // Set volume
        audio.preload = 'auto'; // Preload audio
        audio.crossOrigin = 'anonymous'; // Handle CORS

        console.log('🔊 Audio element created, showing notification...');

        // Show notification
        console.log('🔊 Notification shown, attempting to play...');

        // Handle audio events BEFORE playing
        audio.addEventListener('loadstart', () => {
            console.log('🔊 Audio loading started');
        });

        audio.addEventListener('canplay', () => {
            console.log('🔊 Audio can start playing');
        });

        audio.addEventListener('ended', () => {
            console.log('✅ Audio playback completed');
        });

        audio.addEventListener('error', (e) => {
            console.error('❌ Audio error event:', e);
            console.error('❌ Audio error details:', {
                error: audio.error,
                networkState: audio.networkState,
                readyState: audio.readyState,
                src: audio.src
            });
        });

        // Try to play audio with better error handling
        const playPromise = audio.play();

        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log('✅ Audio playing started on browser speakers');
            }).catch(error => {
                console.error('❌ Audio play failed:', error);
                console.error('❌ Error details:', {
                    name: error.name,
                    message: error.message,
                    audioSrc: audio.src,
                    audioReadyState: audio.readyState
                });

                // Handle specific autoplay policy errors
                if (error.name === 'NotAllowedError') {
                    showMessage('⚠️ Trình duyệt chặn auto-play. Vui lòng click để phát âm thanh.', 'warning');

                    // Create a button for user to manually play
                    createManualPlayButton(audio, fullAudioPath);
                } else {
                }
            });
        }

    } catch (error) {
        console.error('❌ Audio creation failed:', error);
        showMessage('⚠️ Lỗi tạo audio player: ' + error.message, 'warning');
    }
}

// Create manual play button when autoplay is blocked
function createManualPlayButton(audio, audioPath) {
    console.log('🔘 Creating manual play button for:', audioPath);

    // Remove existing play button if any
    const existingBtn = document.getElementById('manualPlayBtn');
    if (existingBtn) {
        existingBtn.remove();
    }

    document.body.appendChild(playBtn);

    // Auto remove button after 10 seconds
    setTimeout(() => {
        if (playBtn.parentNode) {
            playBtn.remove();
        }
    }, 10000);
}

// ========================================
// DOM UTILITIES
// ========================================

// Get element by ID with error handling
function getElementSafely(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`⚠️ Element not found: ${id}`);
    }
    return element;
}

// Set element text content safely
function setTextContent(elementId, text) {
    const element = getElementSafely(elementId);
    if (element) {
        element.textContent = text;
        return true;
    }
    return false;
}

// Set element HTML safely
function setInnerHTML(elementId, html) {
    const element = getElementSafely(elementId);
    if (element) {
        element.innerHTML = html;
        return true;
    }
    return false;
}

// Add class to element safely
function addClassSafely(elementId, className) {
    const element = getElementSafely(elementId);
    if (element) {
        element.classList.add(className);
        return true;
    }
    return false;
}

// Remove class from element safely
function removeClassSafely(elementId, className) {
    const element = getElementSafely(elementId);
    if (element) {
        element.classList.remove(className);
        return true;
    }
    return false;
}

// Toggle class on element safely
function toggleClassSafely(elementId, className) {
    const element = getElementSafely(elementId);
    if (element) {
        element.classList.toggle(className);
        return true;
    }
    return false;
}

// ========================================
// VALIDATION UTILITIES
// ========================================

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate phone number format
function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Sanitize text input
function sanitizeText(text) {
    if (!text) return '';
    return text.toString().trim().replace(/[<>]/g, '');
}

// ========================================
// ARRAY UTILITIES
// ========================================

// Check if array is not empty
function isNotEmptyArray(arr) {
    return Array.isArray(arr) && arr.length > 0;
}

// Get unique values from array
function getUniqueValues(arr) {
    return [...new Set(arr)];
}

// Shuffle array
function shuffleArray(arr) {
    const shuffled = [...arr];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// ========================================
// STRING UTILITIES
// ========================================

// Capitalize first letter
function capitalizeFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Generate random ID
function generateRandomId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// ========================================
// TIME UTILITIES
// ========================================

// Format timestamp
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('vi-VN');
}

// Get time ago string
function getTimeAgo(timestamp) {
    const now = new Date();
    const past = new Date(timestamp);
    const diffMs = now - past;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Vừa xong';
    if (diffMins < 60) return `${diffMins} phút trước`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} giờ trước`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} ngày trước`;
}

// ========================================
// DEBUGGING UTILITIES
// ========================================

// Debug log with timestamp
function debugLog(message, data = null) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 🐛 ${message}`, data || '');
}

// Performance timer
function createTimer(name) {
    const start = performance.now();
    return {
        stop: () => {
            const end = performance.now();
            console.log(`⏱️ ${name}: ${(end - start).toFixed(2)}ms`);
            return end - start;
        }
    };
}

// Export functions to global scope for compatibility
window.showMessage = showMessage;
window.downloadImage = downloadImage;
window.playTTSAudio = playTTSAudio;
window.createManualPlayButton = createManualPlayButton;
window.getElementSafely = getElementSafely;
window.setTextContent = setTextContent;
window.setInnerHTML = setInnerHTML;
window.debugLog = debugLog;
