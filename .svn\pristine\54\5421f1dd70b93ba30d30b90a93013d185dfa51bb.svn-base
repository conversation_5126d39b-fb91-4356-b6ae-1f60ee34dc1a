"""
Migration 005: Reorder columns in TBL_CUSTOMER table
Move customer_name column before company_name column
"""

from database.connection import db

def upgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('''
            CREATE TABLE TBL_CUSTOMER_TEMP (
                customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT,
                company_name TEXT,
                customer_email TEXT,
                customer_tel TEXT,
                customer_addr TEXT,
                customer_title TEXT,
                customer_web TEXT,
                customer_infor TEXT
            )
        '''))
        
        conn.execute(db.text('''
            INSERT INTO TBL_CUSTOMER_TEMP (
                customer_id, customer_name, company_name, customer_email, 
                customer_tel, customer_addr, customer_title, customer_web, customer_infor
            )
            SELECT 
                customer_id, customer_name, company_name, customer_email,
                customer_tel, customer_addr, customer_title, customer_web, customer_infor
            FROM TBL_CUSTOMER
        '''))
        
        conn.execute(db.text('DROP TABLE TBL_CUSTOMER'))
        
        conn.execute(db.text('ALTER TABLE TBL_CUSTOMER_TEMP RENAME TO TBL_CUSTOMER'))
        
        conn.commit()
        print("✅ Successfully reordered columns in TBL_CUSTOMER table")

def downgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('''
            CREATE TABLE TBL_CUSTOMER_TEMP (
                customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_name TEXT,
                customer_name TEXT,
                customer_email TEXT,
                customer_tel TEXT,
                customer_addr TEXT,
                customer_title TEXT,
                customer_web TEXT,
                customer_infor TEXT
            )
        '''))
        
        conn.execute(db.text('''
            INSERT INTO TBL_CUSTOMER_TEMP (
                customer_id, company_name, customer_name, customer_email, 
                customer_tel, customer_addr, customer_title, customer_web, customer_infor
            )
            SELECT 
                customer_id, company_name, customer_name, customer_email,
                customer_tel, customer_addr, customer_title, customer_web, customer_infor
            FROM TBL_CUSTOMER
        '''))
        
        conn.execute(db.text('DROP TABLE TBL_CUSTOMER'))
        
        conn.execute(db.text('ALTER TABLE TBL_CUSTOMER_TEMP RENAME TO TBL_CUSTOMER'))
        
        conn.commit()
        print("✅ Successfully reverted column order in TBL_CUSTOMER table")
