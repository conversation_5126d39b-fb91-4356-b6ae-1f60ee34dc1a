
from database.connection import db

def upgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('''
            CREATE TABLE IF NOT EXISTS TBL_CUSTOMER (
                customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT,
                company_name TEXT,
                customer_email TEXT,
                customer_tel TEXT,
                customer_addr TEXT,
                customer_title TEXT,
                customer_web TEXT,
                customer_infor TEXT
            )
        '''))
        conn.commit()

def downgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('DROP TABLE IF EXISTS TBL_CUSTOMER'))
        conn.commit()
