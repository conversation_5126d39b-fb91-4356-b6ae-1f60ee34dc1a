"""
Migration Runner - Ch<PERSON>y c<PERSON> file migration
"""

import os
import sys
import importlib.util
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from flask import Flask
from database.connection import init_database

class MigrationRunner:
    """Lớp chạy migration"""
    
    def __init__(self):
        """Khởi tạo runner"""
        self.app = Flask(__name__)
        self.db = init_database(self.app)
        self.migrations_dir = os.path.dirname(__file__)
    
    def get_migration_files(self):
        """Lấy danh sách file migration"""
        files = []
        for filename in os.listdir(self.migrations_dir):
            if filename.endswith('.py') and filename.startswith('0') and filename != 'migration_runner.py':
                files.append(filename)
        return sorted(files)
    
    def run_migration(self, filename, action='upgrade'):
        """Chạy m<PERSON>t file migration"""
        try:
            # Import module migration
            file_path = os.path.join(self.migrations_dir, filename)
            spec = importlib.util.spec_from_file_location("migration", file_path)
            migration_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(migration_module)
            
            # Chạy action (upgrade hoặc downgrade)
            with self.app.app_context():
                if action == 'upgrade' and hasattr(migration_module, 'upgrade'):
                    migration_module.upgrade()
                    print(f"✅ Migration {filename} - upgrade thành công")
                elif action == 'downgrade' and hasattr(migration_module, 'downgrade'):
                    migration_module.downgrade()
                    print(f"✅ Migration {filename} - downgrade thành công")
                else:
                    print(f"❌ Không tìm thấy function {action} trong {filename}")
                    
        except Exception as e:
            print(f"❌ Lỗi chạy migration {filename}: {e}")
    
    def migrate_up(self):
        """Chạy tất cả migration lên"""
        files = self.get_migration_files()
        print(f"🔄 Chạy {len(files)} migration files...")
        
        for filename in files:
            self.run_migration(filename, 'upgrade')
    
    def migrate_down(self):
        """Rollback tất cả migration"""
        files = self.get_migration_files()
        files.reverse()  # Chạy ngược lại
        print(f"🔄 Rollback {len(files)} migration files...")
        
        for filename in files:
            self.run_migration(filename, 'downgrade')

def main():
    """Hàm chính"""
    runner = MigrationRunner()
    
    print("🔧 Database Migration Tool")
    print("1. Migrate up (tạo bảng)")
    print("2. Migrate down (xóa bảng)")
    
    choice = input("Chọn chức năng (1-2): ")
    
    if choice == "1":
        runner.migrate_up()
    elif choice == "2":
        runner.migrate_down()
    else:
        print("❌ Lựa chọn không hợp lệ!")

if __name__ == "__main__":
    main()
