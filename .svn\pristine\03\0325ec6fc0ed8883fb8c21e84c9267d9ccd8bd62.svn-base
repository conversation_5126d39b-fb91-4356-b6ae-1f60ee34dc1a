"""
Database Initialization - Khởi tạo cơ sở dữ liệu
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from flask import Flask
from database.connection import init_database
from database.models import Customer, Checkin, CustomerHistory

def create_database():
    """Tạo cơ sở dữ liệu mới"""
    app = Flask(__name__)
    
    # Khởi tạo database
    db = init_database(app)
    
    with app.app_context():
        # Tạo tất cả bảng
        db.create_all()
        print("✅ Database đã được tạo thành công!")
        
if __name__ == "__main__":
    create_database()
