"""
Database Relationships - <PERSON><PERSON> hệ giữa các bảng
"""

# Quan hệ giữa các bảng trong database:

# 1. TBL_CUSTOMER (1) -> TBL_CHECKIN (nhiều)
#    - Một khách hàng có thể có nhiều lần checkin
#    - <PERSON>h<PERSON><PERSON> ngoại: customer_id trong TBL_CHECKIN tham chiếu đến customer_id trong TBL_CUSTOMER
#    - Relationship: Customer.checkins (danh sách checkin của khách hàng)
#    - Relationship: Checkin.customer (thông tin khách hàng của checkin)

# 2. TBL_CUSTOMER (1) -> TBL_CUSTOMER_HIS (nhiều)
#    - Một khách hàng có thể có nhiều lịch sử thay đổi thông tin
#    - Khóa ngoại: customer_id trong TBL_CUSTOMER_HIS tham chiếu đến customer_id trong TBL_CUSTOMER
#    - Relationship: Customer.histories (danh sách lịch sử của khách hàng)
#    - Relationship: CustomerHistory.customer (thông tin khách hàng của lịch sử)

# 3. TBL_CUSTOMER_HIS (1) -> TBL_CHECKIN (nhiều)
#    - Một lịch sử khách hàng có thể có nhiều lần checkin
#    - Khóa ngoại: history_id trong TBL_CHECKIN tham chiếu đến history_id trong TBL_CUSTOMER_HIS
#    - Relationship: CustomerHistory.checkins (danh sách checkin của lịch sử)
#    - Relationship: Checkin.customer_history (thông tin lịch sử của checkin)

# Cách sử dụng:
#
# # Lấy tất cả checkin của một khách hàng
# customer = Customer.query.get(1)
# checkins = customer.checkins
#
# # Lấy thông tin khách hàng từ một checkin
# checkin = Checkin.query.get(1)
# customer = checkin.customer
#
# # Lấy tất cả lịch sử của một khách hàng
# customer = Customer.query.get(1)
# histories = customer.histories
#
# # Lấy thông tin khách hàng từ lịch sử
# history = CustomerHistory.query.get(1)
# customer = history.customer
#
# # Lấy tất cả checkin của một lịch sử
# history = CustomerHistory.query.get(1)
# checkins = history.checkins
#
# # Lấy thông tin lịch sử từ checkin
# checkin = Checkin.query.get(1)
# history = checkin.customer_history
#
# # Tạo checkin mới với cả customer_id và history_id
# new_checkin = Checkin(
#     customer_id=customer.customer_id,
#     history_id=history.history_id,
#     checkin_time=datetime.utcnow(),
#     card_img="path/to/card.jpg"
# )
