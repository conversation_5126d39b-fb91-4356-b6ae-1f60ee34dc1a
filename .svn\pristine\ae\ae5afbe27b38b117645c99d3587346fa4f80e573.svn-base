"""
AI Image Generator - Tr<PERSON>nh tạo ảnh AI sử dụng Gemini 2.0
Chỉ sử dụng prompt từ prompts/prompt.txt
"""

import os  # Thư viện hệ điều hành
import base64  # Thư viện mã hóa base64 cho ảnh
import time  # Thư viện thời gian
from datetime import datetime  # Thư viện ngày tháng
from pathlib import Path  # Thư viện đường dẫn file
from PIL import Image  # Thư viện xử lý ảnh
from io import BytesIO  # Thư viện xử lý dữ liệu nhị phân

# Import path manager để xử lý đường dẫn tập trung - Import path manager for centralized path handling
from utils.path_manager import path_manager

# Import Google GenAI SDK mới cho Gemini 2.0 - Import new Google GenAI SDK for Gemini 2.0 (Fixed according to guide)
try:
    from google import genai  # SDK mới của Google
    from google.genai import types  # Các kiểu dữ liệu của SDK
    NEW_SDK = True  # Đánh dấu đang sử dụng SDK mới
    print("✅ Using new Google GenAI SDK for Gemini 2.0 (Fixed)")
except ImportError as e:
    print(f"❌ Failed to import new SDK: {e}")
    try:
        import google.generativeai as genai  # SDK cũ của Google
        NEW_SDK = False  # Đánh dấu đang sử dụng SDK cũ
        print("⚠️ Using legacy Google GenerativeAI SDK")
    except ImportError:
        print("❌ No Google AI SDK found. Please install google-generativeai or google-genai")
        genai = None  # Không có SDK nào
        NEW_SDK = False

from dotenv import load_dotenv  # Thư viện đọc biến môi trường
from ai_config import load_prompt, get_gemini_config  # Import cấu hình AI

# Tải biến môi trường - Load environment variables
load_dotenv()

class AIImageGenerator:
    """
    Trình tạo ảnh AI - Giai đoạn 2 của Two-Stage AI Pipeline
    AI Image Generator - Stage 2 of Two-Stage AI Pipeline

    Sử dụng Gemini 2.0 Flash Preview Image Generation để tạo ảnh AI với khả năng đa phương tiện:
    Uses Gemini 2.0 Flash Preview Image Generation specifically for creating AI images
    with multimodal capabilities:
    - Kết hợp template prompt, thông tin OCR và ảnh khuôn mặt tham chiếu
    - Combines prompt template, OCR information, and reference face image
    - Tạo nhiều biến thể ảnh với metadata phù hợp
    - Generates multiple image variants with proper metadata
    - Triển khai xử lý lỗi và cơ chế dự phòng
    - Implements error handling and fallback mechanisms

    OPTIMIZED: Singleton pattern để tái sử dụng instance và tăng tốc
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        """Singleton pattern - chỉ tạo 1 instance duy nhất"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Khởi tạo AI Image Generator - chỉ chạy 1 lần với Singleton"""
        if not AIImageGenerator._initialized:
            self.setup_gemini()  # Thiết lập kết nối Gemini API
            AIImageGenerator._initialized = True
            print("🔥 AI Image Generator initialized (Singleton - First time)")
            print("   Model: Gemini 2.0 Flash Preview Image Generation")
            print("   Purpose: Stage 2 - AI Image Generation")
            print("   Features: Multimodal input, multiple variants, metadata tracking")
            print("   Prompt source: prompts/prompt.txt")
        else:
            print("♻️ Reusing existing AI Image Generator instance (Singleton)")
            print("   ⚡ Skipped initialization - Performance optimized!")
            # Reset state cho workflow mới
            self.reset_state()

    def setup_gemini(self):
        """Thiết lập Gemini API - Setup Gemini API (OPTIMIZED with connection reuse)"""
        try:
            # OPTIMIZATION: Kiểm tra xem đã có kết nối chưa - Check if connection already exists
            if hasattr(self, 'client') and self.client is not None:
                print("♻️ Reusing existing Gemini connection")
                print("   ⚡ Skipped API setup - Connection already established!")
                return

            if hasattr(self, 'model') and self.model is not None:
                print("♻️ Reusing existing Gemini model (legacy SDK)")
                print("   ⚡ Skipped API setup - Model already configured!")
                return

            # Tải API key từ biến môi trường - Load API key from environment
            self.load_api_key()  # Gọi hàm tải API key

            if not self.gemini_key:  # Kiểm tra API key có tồn tại không
                raise ValueError("Gemini API key not found")  # Ném lỗi nếu không có key

            if genai is None:  # Kiểm tra SDK có khả dụng không
                raise ValueError("No Google AI SDK available")  # Ném lỗi nếu không có SDK

            # Đặt tên model cho Gemini 2.0 Flash Preview Image Generation
            # Set model name for Gemini 2.0 Flash Preview Image Generation
            self.model_name = "gemini-2.0-flash-preview-image-generation"  # Tên model chính thức

            # Cấu hình dựa trên loại SDK - Configure based on SDK type (according to guide)
            if NEW_SDK:  # Nếu sử dụng SDK mới
                # Sử dụng SDK mới với thiết lập client phù hợp - Use new SDK with proper client setup
                self.client = genai.Client(api_key=self.gemini_key)  # Tạo client với API key
                print(f"✅ Gemini 2.0 API configured with new SDK (First time)")  # Log cấu hình SDK mới
                print(f"   Model: {self.model_name}")  # Log tên model
                print(f"   Target: 30 seconds generation time")  # Log thời gian target
                print(f"   Fixed: Response modalities issue")  # Log đã fix issue
            else:  # Nếu sử dụng SDK cũ
                # Sử dụng SDK cũ làm dự phòng - Use legacy SDK as fallback
                genai.configure(api_key=self.gemini_key)  # Cấu hình API key cho SDK cũ
                self.model = genai.GenerativeModel(self.model_name)  # Tạo model với SDK cũ
                print(f"✅ Gemini 2.0 API configured with legacy SDK (First time)")  # Log cấu hình SDK cũ
                print(f"   Model: {self.model_name}")  # Log tên model

            # OPTIMIZATION: Cache config để tránh load lại - Cache config to avoid reloading
            if not hasattr(self, 'config') or self.config is None:
                self.config = get_gemini_config()  # Lấy cấu hình Gemini từ ai_config
                print("📋 Gemini config loaded and cached")
            else:
                print("♻️ Reusing cached Gemini config")

        except Exception as e:
            print(f"❌ Gemini API setup error: {e}")  # Log lỗi thiết lập API
            raise  # Ném lại exception để caller xử lý

    def reset_state(self):
        """Reset state cho workflow mới - Reset state for new workflow"""
        print("🔄 Resetting AI Image Generator state for new workflow...")
        # Reset any internal state that might cause issues
        # Không reset connection vì Singleton pattern cần giữ connection
        print("✅ AI Image Generator state reset completed")

    def load_api_key(self):
        """Tải Gemini API key từ biến môi trường - Load Gemini API key from environment (Fixed according to guide)"""
        # Lấy tất cả API key có sẵn từ environment variables - Get all available API keys
        api_keys = [
            os.getenv('GEMINI_API_KEY'),  # API key chính từ .env
            os.getenv('GEMINI_API_KEY_2'),  # API key dự phòng 1 từ .env
            os.getenv('GEMINI_API_KEY_3')  # API key dự phòng 2 từ .env
        ]

        # Lọc bỏ key None/rỗng và key placeholder - Filter out None/empty keys and placeholder keys
        valid_keys = [key for key in api_keys if key and key.strip() and not key.startswith('YOUR_')]  # Chỉ lấy key hợp lệ

        if not valid_keys:  # Nếu không có key hợp lệ nào
            self.gemini_key = ''  # Đặt key chính rỗng
            self.backup_keys = []  # Danh sách key dự phòng rỗng
            print("⚠️ No valid Gemini API key found")  # Log cảnh báo không có key
            return  # Thoát khỏi function

        # Sử dụng key hợp lệ đầu tiên làm chính - Use first valid key as primary
        self.gemini_key = valid_keys[0]  # Gán key đầu tiên làm key chính
        self.backup_keys = valid_keys[1:] if len(valid_keys) > 1 else []  # Các key còn lại làm dự phòng

        print(f"✅ Gemini API key loaded for image generation")  # Log tải key thành công
        print(f"   API keys available: {len(valid_keys)}")  # Log số lượng key có sẵn
        print(f"   Primary key: {self.gemini_key[:20]}...")  # Log key chính (ẩn phần sau để bảo mật)
        if self.backup_keys:  # Nếu có key dự phòng
            print(f"   Backup keys available: {len(self.backup_keys)}")  # Log số lượng key dự phòng

    def switch_to_backup_key(self):
        """Chuyển sang API key dự phòng khi bị giới hạn tốc độ - Switch to backup API key when rate limit hit"""
        if not self.backup_keys:  # Nếu không có key dự phòng nào
            print("   ❌ Không còn API key dự phòng nào khả dụng")  # Log không có key dự phòng
            print("   💡 Đề xuất: Chờ reset quota (24h) hoặc sử dụng backup APIs")  # Đề xuất giải pháp
            return False  # Trả về False để báo thất bại

        # Chuyển key hiện tại vào cuối danh sách dự phòng - Move current key to end of backup list
        self.backup_keys.append(self.gemini_key)  # Thêm key hiện tại vào cuối danh sách dự phòng

        # Sử dụng key dự phòng đầu tiên làm key chính mới - Use first backup key as new primary
        self.gemini_key = self.backup_keys.pop(0)  # Lấy key đầu tiên từ danh sách dự phòng

        # Cấu hình lại Gemini với key mới dựa trên loại SDK - Reconfigure Gemini with new key based on SDK type
        if NEW_SDK:  # Nếu sử dụng SDK mới
            self.client = genai.Client(api_key=self.gemini_key)  # Tạo client mới với key mới
        else:  # Nếu sử dụng SDK cũ
            genai.configure(api_key=self.gemini_key)  # Cấu hình lại API key cho SDK cũ

        print(f"   🔄 Đã chuyển sang API key dự phòng: {self.gemini_key[:20]}...")  # Log chuyển sang key dự phòng
        print(f"   📊 Còn lại {len(self.backup_keys)} key dự phòng")  # Log số key dự phòng còn lại

        return True  # Trả về True để báo chuyển key thành công

    def show_quota_exhausted_message(self):
        """Hiển thị thông báo khi tất cả API keys hết quota - Show message when all API keys are exhausted"""
        print("\n" + "="*80)
        print("🚨 TẤT CẢ GEMINI API KEYS ĐÃ HẾT QUOTA")
        print("="*80)
        print("📊 Tình trạng:")
        print("   • Cả 3 Gemini API keys đều đã vượt quá giới hạn 100 requests/ngày")
        print("   • Không thể tạo thêm ảnh AI với Gemini 2.0 Flash Preview")
        print()
        print("💡 Giải pháp đề xuất:")
        print("   1. ⏰ Chờ reset quota tự động (24 giờ kể từ request đầu tiên)")
        print("   2. 💳 Nâng cấp lên Gemini API paid plan tại: https://ai.google.dev/pricing")
        print("   3. 🔄 Sử dụng backup APIs (Hugging Face, OpenAI DALL-E)")
        print("   4. 🔑 Thêm API keys mới vào file .env")
        print()
        print("🔧 Hệ thống sẽ tự động thử backup APIs...")
        print("="*80 + "\n")

    def generate_ai_image(self, face_image_path, card_info, prompt_template='professional', session_id=None):
        """
        Main API method for AI image generation - Wrapper cho generate_dollhouse_image
        Method chính để tạo ảnh AI - tương thích với processing controller

        Args:
            face_image_path (str): Đường dẫn ảnh khuôn mặt
            card_info (dict): Thông tin từ OCR
            prompt_template (str): Template prompt (professional, cartoon, luxury, etc.)
            session_id (str): ID session

        Returns:
            dict: Kết quả tạo ảnh với format chuẩn
        """
        print(f"🎯 AI Image Generation API called")
        print(f"   Face Image: {face_image_path}")
        print(f"   Template: {prompt_template}")
        print(f"   Session: {session_id}")

        # Gọi method chính để tạo ảnh
        return self.generate_dollhouse_image(
            face_path=face_image_path,
            card_info=card_info,
            ai_config={'prompt_template': prompt_template, 'session_id': session_id}
        )

    def generate_dollhouse_image(self, face_path, card_info, ai_config=None):
        """
        GIAI ĐOẠN 2: Tạo ảnh AI sử dụng Gemini 2.0 Flash + dữ liệu OCR + template prompt
        STAGE 2: Generate AI image using Gemini 2.0 Flash + OCR data + prompt template

        Kết hợp - Combines:
        1. Template prompt (phong cách cartoon/luxury/artistic)
        2. Thông tin OCR từ Gemini 2.5 Flash (Giai đoạn 1)
        3. Ảnh khuôn mặt tham chiếu
        """
        try:
            print(f"\n🎨 STAGE 2: AI Image Generation (Gemini 2.0 Flash)")  # Log giai đoạn 2
            print(f"   Face Image: {Path(face_path).name}")  # Log tên file ảnh khuôn mặt
            print(f"   Person: {card_info.get('name', 'Unknown Person')}")  # Log tên người
            print(f"   Company: {card_info.get('company', 'Unknown Company')}")  # Log tên công ty

            # Lấy template prompt từ ai_config - Get prompt template from ai_config
            prompt_template = 'prompt'  # Mặc định - default
            if ai_config and 'prompt_template' in ai_config:  # Nếu có config và template
                prompt_template = ai_config['prompt_template']  # Lấy template từ config

            # Tải template prompt THUẦN TÚY từ file - Load PURE prompt template from file
            pure_prompt = load_prompt(prompt_template)  # Tải prompt thuần túy từ file

            # Thay thế placeholder bằng thông tin thực từ OCR - Replace placeholders with actual OCR data
            final_prompt = self._replace_placeholders_only(pure_prompt, card_info)  # Chỉ thay thế placeholder

            print(f"   🎯 Style Template: {prompt_template}")  # Log template style
            print(f"   📝 Pure Prompt: {len(pure_prompt)} characters")  # Log độ dài prompt thuần túy
            print(f"   ✨ Final Prompt: {len(final_prompt)} characters")  # Log độ dài prompt cuối cùng
            print(f"   🔒 PURE MODE: Only using content from {prompt_template}.txt file")  # Log chế độ thuần túy

            # Tải ảnh khuôn mặt - Load face image
            if not os.path.exists(face_path):  # Nếu file ảnh không tồn tại
                raise FileNotFoundError(f"Face image not found: {face_path}")  # Ném lỗi file không tìm thấy

            face_image = Image.open(face_path)  # Mở ảnh khuôn mặt
            print(f"   Face image loaded: {face_image.size}")  # Log kích thước ảnh

            # Tạo ảnh với nhiều API (chuỗi fallback) - Generate với multiple APIs (fallback chain)
            images_generated = []  # Danh sách ảnh đã tạo

            # [OLD] Tạo 2 biến thể
            # for variant in range(1, 3):
            # [NEW] Tối ưu tốc độ: chỉ tạo 1 biến thể
            for variant in range(1, 2):
                try:
                    # Thử Gemini 2.0 Flash Image Generation TRƯỚC (AI THẬT) - Try Gemini 2.0 Flash Image Generation FIRST (REAL AI)
                    result = self._generate_with_gemini20(final_prompt, face_path, card_info, variant)  # Gọi hàm tạo với Gemini 2.0

                    if result.get('success'):  # Nếu tạo thành công
                        images_generated.append(result['image_path'])  # Thêm đường dẫn ảnh vào danh sách
                        print(f"✅ Variant {variant} successful with Gemini 2.0 Flash AI: {result['image_path']}")  # Log thành công
                    else:  # Nếu tạo thất bại
                        print(f"❌ Gemini 2.0 Flash failed for variant {variant}: {result.get('error')}")  # Log thất bại
                        print(f"❌ Variant {variant} failed - no fallback methods")  # Log thất bại hoàn toàn

                except Exception as e:
                    print(f"❌ Variant {variant} error: {str(e)}")  # Log lỗi biến thể
                    continue  # Tiếp tục với biến thể tiếp theo

            # Trả về kết quả với định dạng nhất quán - Return results with consistent format
            if images_generated:  # Nếu có ảnh được tạo
                return {
                    'success': True,  # Trạng thái thành công
                    'generated_images': images_generated,  # Tên field nhất quán - Consistent field name
                    'image_paths': images_generated,       # Tương thích legacy - Legacy compatibility
                    'image_path': images_generated[0],     # Ảnh chính - Primary image
                    'api_used': 'Gemini 2.0 Flash Preview',  # API đã sử dụng
                    'model_used': self.config['model'],  # Model đã sử dụng
                    'generation_time': f"{len(images_generated)} variants",  # Thời gian tạo
                    'prompt_source': 'prompts/prompt.txt',  # Nguồn prompt
                    'variants_count': len(images_generated)  # Số lượng biến thể
                }
            else:  # Nếu không có ảnh nào được tạo
                return {
                    'success': False,  # Trạng thái thất bại
                    'error': 'No images generated successfully',  # Thông báo lỗi
                    'generated_images': [],  # Danh sách ảnh rỗng
                    'variants_count': 0  # Số biến thể = 0
                }

        except Exception as e:
            print(f"❌ Generation failed: {str(e)}")  # Log tạo ảnh thất bại
            return {
                'success': False,  # Trạng thái thất bại
                'error': str(e)  # Thông báo lỗi
            }

    def _replace_placeholders_only(self, base_prompt, card_info):
        """
        CHỈ THAY THẾ PLACEHOLDER trong prompt với dữ liệu OCR - ONLY replace placeholders in prompt with OCR data

        Không thêm bất kỳ nội dung nào khác vào prompt gốc
        Does not add any other content to the original prompt

        Chỉ thay thế các placeholder như [name], [company], [occupation], [gmail], [phone number]
        Only replaces placeholders like [name], [company], [occupation], [gmail], [phone number]
        """

        # Trích xuất thông tin từ OCR - Extract information from OCR
        name = card_info.get('name', 'Professional').strip()  # Lấy tên hoặc mặc định
        title = card_info.get('title', card_info.get('occupation', 'Professional')).strip()  # Lấy chức vụ
        company = card_info.get('company', 'Company').strip()  # Lấy công ty
        email = card_info.get('email', card_info.get('gmail', '<EMAIL>')).strip()  # Lấy email
        phone = card_info.get('phone', card_info.get('phone_number', '+84 xxx xxx xxx')).strip()  # Lấy số điện thoại

        # CHỈ THAY THẾ PLACEHOLDER - ONLY replace placeholders
        final_prompt = base_prompt
        final_prompt = final_prompt.replace('[name]', name)
        final_prompt = final_prompt.replace('[occupation]', title)
        final_prompt = final_prompt.replace('[company]', company)
        final_prompt = final_prompt.replace('[gmail]', email)
        final_prompt = final_prompt.replace('[phone number]', phone)

        print(f"   🔄 Replaced placeholders: name={name}, title={title}, company={company}")  # Log thay thế
        print(f"   🔒 PURE MODE: No additional content added to prompt")  # Log chế độ thuần túy

        return final_prompt  # Trả về prompt với placeholder đã thay thế

    def _generate_with_gemini20(self, prompt, face_image, card_info, variant):
        """
        Tạo ảnh AI sử dụng Gemini 2.0 Flash Preview Image Generation
        Generate AI image using Gemini 2.0 Flash Preview Image Generation

        Kết hợp - Combines:
        - PURE prompt (chỉ nội dung từ file .txt đã chọn với placeholder được thay thế)
        - Ảnh khuôn mặt tham chiếu - Face reference image
        - Khả năng tạo đa phương tiện - Multimodal generation capabilities
        """
        try:
            print(f"🔥 Gemini 2.0 Flash Preview Generation (variant {variant})...")  # Log bắt đầu tạo với Gemini 2.0
            print(f"   🎯 Using PURE prompt from selected file")  # Log sử dụng prompt thuần túy
            print(f"   👤 Person: {card_info.get('name', 'Unknown')}")  # Log tên người
            print(f"   🏢 Company: {card_info.get('company', 'Unknown')}")  # Log công ty

            # Tải và validate ảnh khuôn mặt - Load and validate face image
            face_path = face_image if isinstance(face_image, str) else None  # Lấy đường dẫn ảnh nếu là string
            if face_path and os.path.exists(face_path):  # Nếu có đường dẫn và file tồn tại
                face_img = Image.open(face_path)  # Mở ảnh từ đường dẫn
                print(f"   📸 Face image loaded: {face_img.size}")  # Log kích thước ảnh đã tải
            else:  # Nếu không có đường dẫn hoặc file không tồn tại
                face_img = face_image  # Sử dụng ảnh được cung cấp trực tiếp
                print(f"   📸 Using provided face image")  # Log sử dụng ảnh được cung cấp

            # Sử dụng prompt THUẦN TÚY từ file đã chọn - Use PURE prompt from selected file
            if prompt and prompt.strip():  # Nếu có prompt và không rỗng
                final_prompt = prompt.strip()  # Loại bỏ khoảng trắng thừa
                print(f"🎯 Using PURE prompt from file: {len(final_prompt)} characters")  # Log độ dài prompt
                print(f"   📝 PURE content from selected .txt file only")  # Log nguồn prompt thuần túy

                # Hiển thị thông tin đã thay thế placeholder - Show replaced placeholder info
                name = card_info.get('name', '')  # Lấy tên từ card info
                company = card_info.get('company', '')  # Lấy công ty từ card info
                if name or company:  # Nếu có tên hoặc công ty
                    print(f"   🔄 Placeholders replaced: Name='{name}', Company='{company}'")  # Log thông tin placeholder đã thay thế
            else:  # Nếu không có prompt
                # KHÔNG CÓ FALLBACK - Báo lỗi nếu không đọc được file prompt
                raise ValueError("❌ PURE MODE: Cannot read prompt file. Please check prompts/ folder and selected template.")  # Báo lỗi không có prompt

            # Quy trình tạo ảnh cốt lõi (Đã sửa theo hướng dẫn) - Core Generation Process (Fixed according to guide)
            if NEW_SDK:  # Nếu sử dụng SDK mới
                # [NEW] Giảm kích thước ảnh mặt và chất lượng để giảm thời gian upload/encode
                from io import BytesIO
                img_byte_arr = BytesIO()

                # Convert RGBA to RGB if needed (JPEG doesn't support transparency)  
                if face_img.mode == 'RGBA':                                          
                    rgb_img = Image.new('RGB', face_img.size, (255, 255, 255))      
                    rgb_img.paste(face_img, mask=face_img.split()[-1])              
                    face_img = rgb_img                                              
                    print("🔄 Converted RGBA to RGB for JPEG compatibility")    

                max_side = 512 
                if max(face_img.size) > max_side:
                    scale = max_side / float(max(face_img.size))
                    new_size = (int(face_img.width * scale), int(face_img.height * scale))
                    face_img = face_img.resize(new_size, Image.Resampling.LANCZOS)
                    print(f"🔻 Resized face to {new_size} for ultra speed")

                face_img.save(img_byte_arr, format='JPEG', quality=60, optimize=True, subsampling=2)
                img_byte_arr = img_byte_arr.getvalue()

                print(f"🎯 Creating multimodal content for Gemini 2.0...")  # Log tạo nội dung đa phương tiện
                print(f"   Text prompt: {len(final_prompt)} characters")  # Log độ dài prompt
                print(f"   Image data: {len(img_byte_arr)} bytes")  # Log kích thước dữ liệu ảnh

                # Tạo nội dung cho Gemini 2.0 (chính xác theo hướng dẫn) - Create content for Gemini 2.0 (exactly as in guide)
                contents = [
                    types.Part.from_text(text=final_prompt),  # Phần text từ prompt
                    types.Part.from_bytes(data=img_byte_arr, mime_type="image/jpeg")  # Phần ảnh từ bytes
                ]

                print("🔥 Generating with Gemini 2.0 Flash Preview Image Generation...")  # Log bắt đầu tạo ảnh
                # Model yêu cầu TEXT, IMAGE → dùng đúng combination, phần TEXT sẽ bỏ qua
                response = self.client.models.generate_content(  # Gọi API tạo nội dung
                    model=self.model_name,  # Tên model
                    contents=contents,  # Nội dung đầu vào
                    config=types.GenerateContentConfig(  # Cấu hình tạo
                        response_modalities=['TEXT', 'IMAGE']
                    )
                )
                print(f"✅ Response received from Gemini 2.0 Flash")  # Log nhận được response
            else:  # Nếu sử dụng SDK cũ
                # Sử dụng SDK cũ - chỉ tạo text hiện tại - Use legacy SDK - text only generation for now
                print("🔄 Using legacy SDK (text-only generation)...")  # Log sử dụng SDK cũ
                print("⚠️ Legacy SDK doesn't support image generation, using fallback...")  # Log SDK cũ không hỗ trợ tạo ảnh
                return {'success': False, 'error': 'Legacy SDK image generation not supported'}  # Trả về lỗi

            print(f"📊 Response received: {type(response)}")  # Log loại response nhận được

            # Kiểm tra xem response có dữ liệu ảnh không - Check if response has image data
            image_data = None  # Khởi tạo dữ liệu ảnh None
            text_response = "Image generated successfully"  # Response text mặc định

            if hasattr(response, 'candidates') and response.candidates:  # Nếu response có candidates
                candidate = response.candidates[0]  # Lấy candidate đầu tiên
                if hasattr(candidate, 'content') and candidate.content:  # Nếu candidate có content
                    content = candidate.content  # Lấy content
                    if hasattr(content, 'parts') and content.parts:  # Nếu content có parts
                        for part in content.parts:  # Lặp qua từng part
                            if hasattr(part, 'inline_data') and part.inline_data:  # Nếu part có inline_data
                                image_data = part.inline_data.data  # Lấy dữ liệu ảnh
                                print("✅ Found image data in response")  # Log tìm thấy dữ liệu ảnh
                                break  # Thoát khỏi vòng lặp
                            elif hasattr(part, 'text') and part.text:  # Nếu part có text
                                text_response = part.text  # Lấy text response
                                print(f"📝 Text response: {text_response[:100]}...")  # Log text response (100 ký tự đầu)

            # Kiểm tra response trực tiếp cho dữ liệu ảnh - Check direct response for image data
            if not image_data and hasattr(response, 'parts'):  # Nếu chưa có dữ liệu ảnh và response có parts
                for part in response.parts:  # Lặp qua từng part trong response
                    if hasattr(part, 'inline_data') and part.inline_data:  # Nếu part có inline_data
                        image_data = part.inline_data.data  # Lấy dữ liệu ảnh
                        print("✅ Found image data in direct response")  # Log tìm thấy dữ liệu ảnh trong response trực tiếp
                        break  # Thoát khỏi vòng lặp

            # Xử lý dữ liệu ảnh nếu tìm thấy - Process image data if found
            if image_data:
                print(f"🖼️ Processing image data: {len(image_data)} bytes")

                # Giải mã và lưu ảnh (Đã sửa theo hướng dẫn) - Decode and save image (Fixed according to guide)
                try:
                    import base64
                    # Kiểm tra dữ liệu đã là bytes hay chuỗi base64 - Check if data is already bytes or base64 string
                    if isinstance(image_data, str):
                        image_bytes = base64.b64decode(image_data)  # Giải mã base64 thành bytes
                    else:
                        image_bytes = image_data  # Dữ liệu đã là bytes

                    # Tạo đối tượng BytesIO và mở ảnh - Create BytesIO object and open image
                    image_buffer = BytesIO(image_bytes)
                    image = Image.open(image_buffer)
                    print(f"✅ Image decoded successfully: {image.size}")
                except Exception as decode_error:
                    print(f"❌ Image decode error: {decode_error}")
                    # Thử phương pháp giải mã thay thế - Try alternative decode methods
                    try:
                        # Phương pháp 2: Bytes trực tiếp - Method 2: Direct bytes
                        image = Image.open(BytesIO(image_data))
                        print(f"✅ Alternative decode successful: {image.size}")
                    except Exception as alt_error:
                        print(f"❌ Alternative decode failed: {alt_error}")
                        raise decode_error

                # Lưu ảnh sử dụng PathManager - Save image using PathManager
                person_name = card_info.get('name', 'person')
                output_path = path_manager.get_ai_image_path(person_name, variant, prefix="gemini_ai")

                image.save(output_path)
                print(f"✅ REAL AI image saved: {output_path}")

                return {
                    'success': True,
                    'image_path': str(output_path),
                    'size': image.size,
                    'type': 'gemini_real_ai'
                }
            else:
                # Nếu không tìm thấy ảnh - If no image found
                print("⚠️ No image data found in Gemini response")
                if hasattr(response, 'text') and response.text:
                    print(f"📝 Response text: {response.text[:200]}...")

                # No fallback - return error
                return {'success': False, 'error': 'No image data found in Gemini response'}

        except Exception as e:
            print(f"   ❌ Gemini Image Generation error: {e}")

            # Kiểm tra xem có phải lỗi giới hạn tốc độ hoặc server quá tải không - Check if it's a rate limit or server overload error
            error_str = str(e).lower()
            if 'rate limit' in error_str or 'quota' in error_str or '429' in error_str or '503' in error_str or 'unavailable' in error_str or 'overloaded' in error_str:
                if '503' in error_str or 'unavailable' in error_str or 'overloaded' in error_str:
                    print("   🔄 Phát hiện lỗi server quá tải (503), đang thử API key dự phòng...")
                else:
                    print("   🔄 Phát hiện lỗi giới hạn quota, đang thử API key dự phòng...")

                if self.switch_to_backup_key():
                    # Thử lại với key dự phòng - Retry with backup key
                    try:
                        if '503' in error_str or 'unavailable' in error_str or 'overloaded' in error_str:
                            print("   ⏳ Đợi 3 giây trước khi thử lại do server quá tải...")
                            import time
                            time.sleep(3)
                        print("   🔄 Đang thử lại với API key dự phòng...")
                        return self._generate_with_gemini20(prompt, face_image, card_info, variant)
                    except Exception as retry_error:
                        retry_error_str = str(retry_error).lower()
                        if 'rate limit' in retry_error_str or 'quota' in retry_error_str or '429' in retry_error_str or '503' in retry_error_str or 'unavailable' in retry_error_str or 'overloaded' in retry_error_str:
                            if '503' in retry_error_str or 'unavailable' in retry_error_str or 'overloaded' in retry_error_str:
                                print(f"   ❌ API key dự phòng cũng gặp lỗi server quá tải: {retry_error}")
                            else:
                                print(f"   ❌ API key dự phòng cũng hết quota: {retry_error}")
                            # Thử tiếp key dự phòng khác nếu còn
                            if self.backup_keys:
                                print("   🔄 Thử API key dự phòng tiếp theo...")
                                return self._generate_with_gemini20(prompt, face_image, card_info, variant)
                            else:
                                if '503' in retry_error_str or 'unavailable' in retry_error_str or 'overloaded' in retry_error_str:
                                    print("   ❌ Tất cả Gemini API keys đều gặp lỗi server quá tải")
                                    print("   💡 Giải pháp: Đợi vài phút và thử lại, hoặc sử dụng backup APIs")
                                else:
                                    print("   ❌ Tất cả Gemini API keys đều hết quota")
                                    print("   💡 Giải pháp: Chờ reset quota (24h) hoặc sử dụng backup APIs")
                        else:
                            print(f"   ❌ API key dự phòng gặp lỗi khác: {retry_error}")
                else:
                    self.show_quota_exhausted_message()  # Hiển thị thông báo chi tiết

            import traceback
            traceback.print_exc()

            # No fallback - return error
            return {'success': False, 'error': str(e)}



        except Exception as e:
            print(f"   ❌ Exception in _generate_with_gemini20: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }




