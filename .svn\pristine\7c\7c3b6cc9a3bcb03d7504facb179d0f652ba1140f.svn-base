
from typing import List, Optional
from datetime import datetime
from database.models.customer_history import CustomerHistory
from database.connection import db
from .base import BaseCRUD

class CustomerHistoryCRUD(BaseCRUD):
    
    def __init__(self):
        super().__init__(CustomerHistory)
    
    def create_history(self, customer_id: int, **customer_data) -> CustomerHistory:
        return self.create(
            customer_id=customer_id,
            history_time=datetime.now(),
            **customer_data
        )
    
    def get_by_customer(self, customer_id: int) -> List[CustomerHistory]:
        return CustomerHistory.query.filter_by(customer_id=customer_id).order_by(CustomerHistory.history_time.desc()).all()
    
    def get_latest_history(self, customer_id: int) -> Optional[CustomerHistory]:
        return CustomerHistory.query.filter_by(customer_id=customer_id).order_by(CustomerHistory.history_time.desc()).first()
    
    def get_history_by_date_range(self, start_date: datetime, end_date: datetime) -> List[CustomerHistory]:
        return CustomerHistory.query.filter(
            CustomerHistory.history_time >= start_date,
            CustomerHistory.history_time <= end_date
        ).order_by(CustomerHistory.history_time.desc()).all()
    
    def get_with_customer_info(self, history_id: int) -> CustomerHistory:
        from sqlalchemy.orm import joinedload
        return CustomerHistory.query.options(joinedload(CustomerHistory.customer)).get(history_id)
    
    def get_with_checkins(self, history_id: int) -> CustomerHistory:
        from sqlalchemy.orm import joinedload
        return CustomerHistory.query.options(joinedload(CustomerHistory.checkins)).get(history_id)
    
    def create_from_customer(self, customer_id: int) -> CustomerHistory:
        from .customer_crud import CustomerCRUD
        
        customer_crud = CustomerCRUD()
        customer = customer_crud.get_by_id_or_404(customer_id)
        
        return self.create_history(
            customer_id=customer_id,
            company_name=customer.company_name,
            customer_addr=customer.customer_addr,
            customer_name=customer.customer_name,
            customer_title=customer.customer_title,
            customer_email=customer.customer_email,
            customer_tel=customer.customer_tel,
            customer_infor=customer.customer_infor
        )

    def compare_with_current(self, history_id: int) -> dict:
        history = self.get_by_id_or_404(history_id)

        from .customer_crud import CustomerCRUD
        customer_crud = CustomerCRUD()
        current_customer = customer_crud.get_by_id_or_404(history.customer_id)

        changes = {}
        fields = ['company_name', 'customer_addr', 'customer_name',
                 'customer_title', 'customer_email', 'customer_tel', 'customer_infor']

        for field in fields:
            history_value = getattr(history, field)
            current_value = getattr(current_customer, field)
            if history_value != current_value:
                changes[field] = {
                    'old': history_value,
                    'new': current_value
                }

        return changes
