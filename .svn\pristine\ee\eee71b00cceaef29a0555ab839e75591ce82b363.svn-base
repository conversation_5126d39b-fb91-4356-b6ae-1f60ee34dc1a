import cv2
import time
import os
import sys
import numpy as np
from scrfd import SCRFD
import threading
current_dir = os.path.dirname(__file__)
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


from utils.path_manager import get_captured_image_path

from api_trigger import trigger_auto_capture

# Import FaceTTS
from face_tts import (
    speak_and_play_no_face, 
    speak_and_play_multiple_faces, 
    speak_and_play_face_too_far,
    speak_and_play_face_direction,
    speak_and_play_keep_face,
    speak_and_play_face_lost_countdown,
    speak_and_play_capture_success,
    speak_and_play_welcome  # Thêm import cho welcome message
)

def should_speak_tts(last_tts_time, tts_cooldown):
    current_time = time.time()
    if current_time - last_tts_time >= tts_cooldown:
        return True, current_time
    return False, last_tts_time

def run_face_capture(save_dir="image", model_path="scrfd_10g_bnkps.onnx", frame_source=None, camera_controller=None):
    current_message = ""
    camera_index = 0
    os.makedirs(save_dir, exist_ok=True)

    detector = SCRFD(model_file=model_path)
    detector.prepare(ctx_id=-1)

    if frame_source is None:
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            print("Không mở được webcam")
            return
        use_external_frames = False
    else:
        cap = None
        use_external_frames = True

    if use_external_frames:
        frame_width, frame_height = 640, 480
    else:
        frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    center_x, center_y = frame_width // 2, frame_height // 2
    center_tolerance = 180
    offset_tolerance = 100

    min_face_size = 100
    stable_threshold = 10
    face_stable_counter = 0

    countdown_mode = False
    countdown_start_time = None
    countdown_seconds = 0
    saved = False

    delay_between_captures = 10
    last_save_time = 0
    blur_strength = 31  
    
    # Biến kiểm soát TTS
    last_tts_time = 0
    tts_cooldown = 10.0
    
    welcome_played = False
    if camera_controller and camera_controller.session_model.current_session:
        welcome_played = camera_controller.session_model.current_session.get('welcome_voice_played', False)
    
    previous_state = "none"
    previous_direction = ""
    
    # Biến stream
    stream_paused = False
    pause_start_time = None
    pause_duration = 3.0  
    
    while True:
        if camera_controller and not camera_controller.streaming_active_1:
            print("🛑 Face stream paused by camera controller")
            time.sleep(0.005)
            continue
            
        if camera_controller and camera_controller.capture_mode_1:
            print("📸 Face camera in capture mode - pausing stream")
            time.sleep(0.005)
            continue
        
        if stream_paused:
            if time.time() - pause_start_time >= pause_duration:
                stream_paused = False
                print("🔄 Face stream resumed after capture")
            else:
                if saved and captured_image is not None:
                    ret, buffer = cv2.imencode('.jpg', captured_image)
                    frame = buffer.tobytes()
                    yield (b'--frame\r\n'
                        b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
                time.sleep(0.005)
                continue
        
        if use_external_frames:
            frame = frame_source()
            if frame is None:
                continue
        else:
            ret, frame = cap.read()
            if not ret:
                break

        display_frame = frame.copy()
        bboxes, landmarks = detector.detect(frame, thresh=0.4)

        # Vẽ ROI ellipse
        axes_length = (center_tolerance, int(center_tolerance * 1.25))
        # Tạo mask trắng trong ROI, đen ngoài ROI (1 kênh)
        roi_mask = np.zeros((frame_height, frame_width), dtype=np.uint8)
        cv2.ellipse(roi_mask, (center_x, center_y), axes_length, 0, 0, 360, 255, -1)

        # Làm mờ nền
        background = cv2.GaussianBlur(display_frame, (51, 51), 0)

        # Tạo vùng trong ROI (mask 3 kênh)
        roi_mask_3c = cv2.merge([roi_mask, roi_mask, roi_mask])

        # Giữ nguyên trong ROI, ngoài ROI lấy background mờ
        display_frame = np.where(roi_mask_3c == 255, display_frame, background)


        # Vẽ viền ellipse cho dễ nhìn
        cv2.ellipse(display_frame, (center_x, center_y), axes_length, 0, 0, 360, (0, 255, 255), 2)

        
        # blurred = cv2.GaussianBlur(display_frame, (blur_strength, blur_strength), 0)
        # display_frame = np.where(mask == 255, display_frame, blurred)

        if countdown_mode:
            start_angle = int((time.time() * 200) % 360)
            end_angle = start_angle + 60
            cv2.ellipse(display_frame, (center_x, center_y), axes_length, 0, start_angle, end_angle, (0, 255, 0), 4)
        else:
            cv2.ellipse(display_frame, (center_x, center_y), axes_length, 0, 0, 360, (255, 255, 0), 2)

        if saved and time.time() - last_save_time >= delay_between_captures:
            saved = False
            
        current_state = "none"
        current_direction = ""
        
        # Phát voice welcome khi phát hiện khuôn mặt lần đầu
        if len(bboxes) > 0 and not welcome_played:
            can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
            if can_speak:
                speak_and_play_welcome()
                welcome_played = True
                if camera_controller and camera_controller.session_model.current_session:
                    camera_controller.session_model.update_session(welcome_voice_played=True)
                print("Đã phát voice welcome khi phát hiện khuôn mặt")
        
        # ============================================================
        # 🔥 Chỉ xét khuôn mặt trong vùng ROI
        # ============================================================
        faces_in_center = []
        for bbox in bboxes:
            x1, y1, x2, y2, _ = bbox.astype(int)
            w, h = x2 - x1, y2 - y1
            face_cx, face_cy = (x1 + x2) // 2, (y1 + y2) // 2

            if w >= min_face_size and h >= min_face_size and \
               abs(face_cx - center_x) <= offset_tolerance and \
               abs(face_cy - center_y) <= offset_tolerance:
                faces_in_center.append((x1, y1, x2, y2))
                cv2.rectangle(display_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.circle(display_frame, (face_cx, face_cy), 5, (0, 255, 255), -1)

        # ============================================================
        # Xử lý theo số lượng khuôn mặt trong ROI
        # ============================================================
        if len(faces_in_center) == 0:
            current_state = "no_face_in_center"
            if current_state != previous_state:
                can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                if can_speak:
                    speak_and_play_no_face()
            saved = False

        else:
            # ✅ Chỉ lấy khuôn mặt đầu tiên trong ROI
            x1, y1, x2, y2 = faces_in_center[0]
            w, h = x2 - x1, y2 - y1
            face_cx, face_cy = (x1 + x2) // 2, (y1 + y2) // 2

            dx = face_cx - center_x
            dy = face_cy - center_y

            if abs(dx) > offset_tolerance or abs(dy) > offset_tolerance:
                current_state = "direction"
                directions = []
                if abs(dx) > offset_tolerance:
                    direction = "left" if dx < 0 else "right"
                    directions.append(direction)
                if abs(dy) > offset_tolerance:
                    direction = "up" if dy > 0 else "down"
                    directions.append(direction)
                
                current_direction = "_".join(sorted(directions))
                current_message = "Please put your face " + " and ".join(directions)
                
                if current_state != previous_state or current_direction != previous_direction:
                    can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                    if can_speak and directions:
                        speak_and_play_face_direction(directions[0])
                
                face_stable_counter = 0
                if countdown_mode:
                    countdown_mode = False
                    countdown_start_time = None
            else:
                face_stable_counter += 1
                if face_stable_counter >= stable_threshold and not countdown_mode and not saved:
                    current_state = "countdown"
                    countdown_mode = True
                    countdown_start_time = time.time()
                    face_stable_counter = 0
                    current_message = "Keep your face"
                    if current_state != previous_state:
                        can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                        if can_speak:
                            speak_and_play_keep_face()
                else:
                    current_state = "stable"

        previous_state = current_state
        previous_direction = current_direction

        # ============================================================
        # Countdown và capture
        # ============================================================
        if countdown_mode:
            if len(faces_in_center) == 0:
                countdown_mode = False
                countdown_start_time = None
            else:
                elapsed = int(time.time() - countdown_start_time)
                remaining = countdown_seconds - elapsed
                if remaining <= 0:
                    # Chụp ảnh gốc để dùng
                    captured_image = frame.copy()
                    saved = True
                    last_save_time = time.time()
                    countdown_mode = False
                    print("✅ Đã chụp ảnh")

                    # 🚀 Lưu file và gọi API trên thread riêng để không block
                    def save_and_trigger(image):
                        if trigger_auto_capture():
                            print("📡 Auto capture triggered via API")
                        else:
                            timestamp = int(time.time())
                            filename = get_captured_image_path('face', timestamp)
                            cv2.imwrite(filename, image)
                            print(f"💾 Đã lưu ảnh: {filename}")

                    threading.Thread(target=save_and_trigger, args=(captured_image,), daemon=True).start()

                    # Tạm dừng stream (vẫn hiển thị ảnh vừa chụp)
                    stream_paused = True
                    pause_start_time = time.time()

                    if camera_controller:
                        camera_controller.capture_mode_1 = True

                    # Thông báo thành công
                    can_speak, last_tts_time = should_speak_tts(last_tts_time, tts_cooldown)
                    if can_speak:
                        speak_and_play_capture_success()
        
        if saved and captured_image is not None: 
            ret, buffer = cv2.imencode('.jpg', captured_image)
        else:
            ret, buffer = cv2.imencode('.jpg', display_frame)

        frame = buffer.tobytes()
        yield (b'--frame\r\n'
            b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
