"""
Test Database Connection - Ki<PERSON>m tra kết nối cơ sở dữ liệu
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from flask import Flask
from database.connection import init_database
from database.models import Customer, Checkin, CustomerHistory

def test_connection():
    """Kiểm tra kết nối database"""
    try:
        print("🔄 Đang kiểm tra kết nối SQLite...")

        # Tạo thư mục database/data nếu chưa có
        data_dir = 'database/data'
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"✅ Đã tạo thư mục: {data_dir}")

        # Tạo Flask app
        app = Flask(__name__)

        # Khởi tạo database
        db = init_database(app)
        
        with app.app_context():
            # Kiểm tra kết nối
            with db.engine.connect() as conn:
                result = conn.execute(db.text('SELECT 1')).fetchone()
            if result:
                print("✅ Kết nối SQLite thành công!")
                
                # Kiểm tra đường dẫn database
                db_path = app.config['SQLALCHEMY_DATABASE_URI']
                print(f"📁 Đường dẫn database: {db_path}")
                
                # Kiểm tra file database có tồn tại không
                actual_path = db_path.replace('sqlite:///', '')
                if os.path.exists(actual_path):
                    print(f"✅ File database tồn tại: {actual_path}")
                    file_size = os.path.getsize(actual_path)
                    print(f"📊 Kích thước file: {file_size} bytes")
                else:
                    print(f"⚠️ File database chưa tồn tại: {actual_path}")
                
                # Kiểm tra các bảng
                check_tables(db)
                
                return True
            else:
                print("❌ Không thể kết nối SQLite!")
                return False
                
    except Exception as e:
        print(f"❌ Lỗi kết nối: {e}")
        return False

def check_tables(db):
    """Kiểm tra các bảng trong database"""
    try:
        print("\n🔍 Kiểm tra các bảng:")
        
        # Lấy danh sách bảng
        with db.engine.connect() as conn:
            tables = conn.execute(db.text("SELECT name FROM sqlite_master WHERE type='table'")).fetchall()
        
        if tables:
            print(f"📋 Có {len(tables)} bảng:")
            for table in tables:
                table_name = table[0]
                print(f"   - {table_name}")
                
                # Đếm số record trong bảng
                try:
                    with db.engine.connect() as conn:
                        count = conn.execute(db.text(f"SELECT COUNT(*) FROM {table_name}")).fetchone()[0]
                        print(f"     📊 Số record: {count}")
                except:
                    print(f"     ⚠️ Không thể đếm record")
        else:
            print("📋 Chưa có bảng nào trong database")
            
    except Exception as e:
        print(f"❌ Lỗi kiểm tra bảng: {e}")

def test_models():
    """Kiểm tra các model"""
    try:
        print("\n🔍 Kiểm tra các model:")

        # Tạo thư mục database/data nếu chưa có
        data_dir = 'database/data'
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        app = Flask(__name__)
        db = init_database(app)
        
        with app.app_context():
            # Test tạo customer
            customer = Customer(
                company_name="Test Company",
                customer_name="Test User",
                customer_email="<EMAIL>"
            )
            db.session.add(customer)
            db.session.commit()
            print("✅ Tạo Customer thành công!")
            
            # Test query customer
            found_customer = Customer.query.filter_by(customer_name="Test User").first()
            if found_customer:
                print(f"✅ Query Customer thành công: {found_customer.customer_name}")
            
            # Xóa test data
            db.session.delete(customer)
            db.session.commit()
            print("✅ Xóa test data thành công!")
            
    except Exception as e:
        print(f"❌ Lỗi test model: {e}")

if __name__ == "__main__":
    print("🧪 Test Database Connection")
    print("=" * 40)
    
    # Test kết nối
    if test_connection():
        # Test models nếu kết nối thành công
        test_models()
    
    print("\n✅ Hoàn thành test!")
