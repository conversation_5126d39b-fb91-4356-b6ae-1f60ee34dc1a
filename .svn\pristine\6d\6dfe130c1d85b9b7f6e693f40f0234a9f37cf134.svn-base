/* ========================================
   RESPONSIVE DESIGN CSS - Media queries and breakpoints
   Responsive styles for different screen sizes and devices
   Includes Apple Design System utilities and responsive enhancements
   ======================================== */

/* ===== UTILITIES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* ===== VIBRANT UTILITIES ===== */
.vibrant-text-glow {
  text-shadow: 0 0 20px currentColor;
}

.vibrant-border-glow {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              var(--primary-gradient) border-box;
}

.vibrant-shadow-primary {
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.vibrant-shadow-secondary {
  box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3);
}

.vibrant-backdrop {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

/* Specific 1920x1080 optimization - Tối ưu cụ thể cho 1920x1080 */
@media (min-width: 1920px) and (max-height: 1080px) {
    .single-page-container {
        margin-top: 50px;                      
        padding: 0 !important;                  
        width: 100% !important;                  
    }

    .workflow-section {
        margin-bottom: var(--spacing-xs);        /* Khoảng cách tối thiểu */
    }

    /* Camera section responsive - Điều chỉnh gradient cho màn hình 1920x1080 */
    #cameraSection {
        border-width: 1px;                        /* Giảm độ dày viền */
    }

    .camera-grid {
        min-height: 300px;                       /* Chiều cao tối ưu */
        gap: var(--spacing-sm);                  /* Gap cân đối */
        padding: 0 !important;                   /* NO PADDING for full width */
        width: 100% !important;                  /* FULL WIDTH */
    }

    .camera-frame {
        width: 99% !important;                  /* MAXIMUM SIZE for 1920x1080 */
        padding-bottom: 74.25% !important;      /* Optimized aspect ratio */
        border-radius: 6px !important;          /* Minimal border radius */
        border-width: 1px !important;           /* Minimal border */
    }

    .combined-controls {
        margin-top: 0;                           /* Không margin top */
        padding: var(--spacing-xs);              /* Padding tối thiểu */
    }
}

/* Large desktop screens - Màn hình desktop lớn (1920px+) */
@media (min-width: 1920px) {
    /* Tối ưu cho màn hình 1920x1080 - ABSOLUTE FULL WIDTH */
    .single-page-container {
        width: 100vw !important;                 /* VIEWPORT WIDTH - absolute full screen */
        padding: 0 !important;                   /* NO PADDING whatsoever */
        margin: 50px 0 0 0 !important;           /* Only top margin, no side margins */
    }

    .camera-main-container {
        width: 100vw !important;                 /* VIEWPORT WIDTH - absolute full screen */
        padding: 0 !important;                   /* NO PADDING whatsoever */
        margin: 0 !important;                    /* NO margins */
    }

    .camera-grid {
        width: 100vw !important;                 /* VIEWPORT WIDTH - absolute full screen */
        padding: 0 !important;                   /* NO PADDING whatsoever */
        margin: 0 !important;                    /* NO margins */
    }

    .workflow-section {
        margin-bottom: var(--spacing-sm);        /* Giảm khoảng cách giữa sections */
    }

    /* Camera section responsive - Điều chỉnh gradient cho màn hình lớn */
    #cameraSection {
        border-width: 3px;                        /* Tăng độ dày viền cho màn hình lớn */
    }

    .section-body {
        padding: var(--spacing-sm);              /* Giảm padding section body */
    }

    .camera-grid {
        gap: var(--spacing-md);                  /* Gap cân đối */
        margin-bottom: var(--spacing-sm);        /* Margin bottom nhẹ */
        min-height: 350px;                       /* Chiều cao tối ưu cho màn hình lớn */
        padding: 0 !important;                   /* NO PADDING for full width */
        width: 100% !important;                  /* FULL WIDTH */
    }

    .camera-frame {
        width: 99.5% !important;                /* ABSOLUTE MAXIMUM SIZE */
        padding-bottom: 74.6% !important;       /* Optimized aspect ratio */
        border-radius: 8px !important;          /* Minimal border radius */
        border-width: 1px !important;           /* Minimal border */
    }

    .combined-controls {
        padding: var(--spacing-sm);              /* Giảm padding controls */
        margin-top: var(--spacing-xs);           /* Margin top tối thiểu */
    }

    .combined-controls .control-group {
        min-height: 100px;                       /* Giảm chiều cao tối thiểu */
        padding: var(--spacing-xs);              /* Giảm padding */
        justify-content: center;                 /* Đảm bảo căn giữa */
    }

    .combined-controls .controls-grid {
        gap: var(--spacing-sm);                  /* Giảm gap cho màn hình lớn */
    }
}

/* Desktop screens - Màn hình desktop (1200px - 1920px) */
@media (min-width: 1200px) and (max-width: 1919px) {
    .single-page-container {
        width: 100% !important;                  /* FULL WIDTH - no max-width limit */
        padding: 0 !important;                   /* NO PADDING for true full width */
    }

    .camera-grid {
        min-height: 400px;
        gap: var(--spacing-md);
    }

    .camera-frame {
        padding-bottom: 75%;
    }

    .images-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .card-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

/* Tablet and small desktop - Màn hình tablet và desktop nhỏ */
@media (max-width: 1200px) {
    /* Main content grid - Chuyển từ 2 cột thành 1 cột */
    .main-content-grid {
        grid-template-columns: 1fr;                     /* 1 cột duy nhất */
        gap: var(--spacing-xl);                          /* Khoảng cách lớn */
    }

    /* Results grid - Chuyển từ 2 cột thành 1 cột */
    .results-grid {
        grid-template-columns: 1fr;                     /* 1 cột duy nhất */
    }

    .single-page-container {
        max-width: 100%;
        padding: var(--spacing-md);
    }

    .camera-grid {
        min-height: 350px;
        gap: var(--spacing-sm);
    }

    .images-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .card-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* Tablet devices - Thiết bị tablet */
@media (max-width: 1024px) {
    /* Navigation adjustments - Điều chỉnh navigation */
    .apple-nav-content {
        padding: 0 var(--spacing-md) !important;        /* Padding nhỏ hơn */
    }

    .nav-title {
        font-size: 24px !important;                     /* Font nhỏ hơn */
    }

    /* Logo positioning on tablet */
    .apple-logo {
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    .camera-grid {
        min-height: 300px;
    }

    .camera-frame {
        padding-bottom: 80%;
    }

    .controls-grid {
        gap: var(--spacing-lg);
    }

    .images-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }
}

/* Mobile devices - Thiết bị di động */
@media (max-width: 768px) {
    /* Navigation mobile - Navigation trên mobile */
    .apple-nav {
        height: 70px !important;                        /* Chiều cao nhỏ hơn */
    }

    .nav-logo-img {
        height: 50px !important;                        /* Logo nhỏ hơn */
    }

    .nav-title {
        font-size: 20px !important;                     /* Font nhỏ hơn */
    }

    /* Logo positioning on mobile */
    .apple-logo {
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Single page container - Giảm padding cho mobile */
    .single-page-container {
        padding: var(--spacing-md);                     /* Padding nhỏ hơn */
        margin-top: 50px;                               /* Margin top nhỏ hơn */
    }

    /* Camera grid - Chuyển thành 1 cột trên mobile */
    .camera-grid {
        grid-template-columns: 1fr;                     /* 1 cột duy nhất */
        gap: var(--spacing-lg);                         /* Khoảng cách vừa */
        min-height: 250px;
    }

    .camera-frame {
        padding-bottom: 75%;
    }

    /* Combined controls - Điều chỉnh controls cho mobile */
    .combined-controls {
        padding: var(--spacing-lg);                     /* Padding nhỏ hơn */
        margin-top: var(--spacing-xl);                  /* Margin top nhỏ hơn */
    }

    /* Combined controls grid - Chuyển thành 1 cột trên mobile */
    .combined-controls .controls-grid {
        grid-template-columns: 1fr !important;          /* 1 cột duy nhất */
        gap: var(--spacing-lg) !important;              /* Khoảng cách vừa */
    }

    /* Images grid mobile */
    .images-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* Card info grid mobile */
    .card-info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    /* Button adjustments */
    .button-group-container {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .main-action-button,
    .retake-button-adjacent {
        width: 100%;
        max-width: none;
    }
}

/* Small mobile devices - Thiết bị mobile nhỏ */
@media (max-width: 480px) {
    /* Navigation very small - Navigation rất nhỏ */
    .apple-nav-content {
        padding: 0 var(--spacing-sm) !important;        /* Padding rất nhỏ */
    }

    .nav-title {
        font-size: 18px !important;                     /* Font rất nhỏ */
    }

    /* Logo positioning on small mobile */
    .apple-logo {
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Container very small - Container rất nhỏ */
    .single-page-container {
        padding: var(--spacing-sm);                     /* Padding rất nhỏ */
    }

    /* Combined controls very small - Controls rất nhỏ */
    .combined-controls {
        padding: var(--spacing-md);                     /* Padding nhỏ */
    }

    .camera-grid {
        min-height: 200px;
        padding: var(--spacing-xs);
    }

    .camera-frame {
        padding-bottom: 70%;
    }

    /* Typography adjustments */
    .page-title {
        font-size: clamp(18px, 4vw, 24px) !important;
    }

    .section-title {
        font-size: 20px;
    }

    /* Animations on mobile - Animations trên mobile */
    .section-hidden {
        transform: translateY(20px);                    /* Dịch chuyển nhỏ hơn */
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);                /* Dịch chuyển nhỏ hơn */
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* ========================================
   PRINT STYLES
   ======================================== */

@media print {
    .apple-nav,
    .combined-controls,
    .camera-section,
    .download-btn {
        display: none !important;
    }

    .single-page-container {
        margin: 0;
        padding: 0;
        max-width: none;
    }

    .workflow-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .card-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .images-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ========================================
   HIGH CONTRAST MODE
   ======================================== */

@media (prefers-contrast: high) {
    .workflow-section {
        border: 2px solid #000;
        background: #fff;
        color: #000;
    }

    .section-header {
        background: #000;
        color: #fff;
    }

    .camera-container {
        border: 2px solid #000;
    }

    .apple-button {
        background: #000 !important;
        color: #fff !important;
        border: 2px solid #000 !important;
    }
}

/* ========================================
   REDUCED MOTION
   ======================================== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .section-hidden,
    .section-visible {
        transition: none;
    }

    .slide-in,
    .fade-in {
        animation: none;
    }

    .apple-fade-in,
    .apple-scale-in,
    .apple-loading {
        animation: none;
    }
}

/* ========================================
   APPLE FOCUS STATES
   ======================================== */

.apple-button:focus,
.apple-select:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.25);
}

.apple-button:focus-visible,
.apple-select:focus-visible {
  outline: 3px solid rgba(102, 126, 234, 0.5);
  outline-offset: 2px;
}

:root { --nav-height: 60px; }
@media (max-width: 768px) {
  :root { --nav-height: 70px; }
}

#cameraSection .section-body {
  min-height: calc(100dvh - var(--nav-height)) !important;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-xs) !important;
}

#cameraSection .section-body .camera-grid {
  height: 100% !important;
  min-height: 0 !important;
  align-items: stretch !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 4px !important;
}

#cameraSection { border-width: 2px !important; }


#cameraSection .section-body {
  width: 100% !important;
  height: calc(100dvh - var(--nav-height)) !important; 
  min-height: unset !important;
}
#cameraSection .section-body .camera-grid {
  width: 100% !important;
  height: 100% !important; 
}
#cameraSection .camera-container { height: 100% !important; }
#cameraSection .camera-frame { height: 100% !important; padding-bottom: 0 !important; }
#cameraSection .camera-stream { width: 100% !important; height: 100% !important; object-fit: cover !important; }


#cameraSection .section-body .camera-grid {
  min-height: 0 !important;            
  grid-auto-rows: 1fr !important;       
}
#cameraSection .camera-container {
  min-height: 0 !important;             
  max-height: none !important;          
}


#cameraSection .section-body .camera-grid {
  grid-template-rows: 1fr !important;   
  align-content: stretch !important;
  align-items: stretch !important;
}

#cameraSection .section-body .camera-grid > .camera-container {
  height: 100% !important;
  align-self: stretch !important;
}
