fsspec-2025.9.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fsspec-2025.9.0.dist-info/METADATA,sha256=VmMoGluoRhQXlQigYs9kzwlXfPIg1KBkRL7V2F5O2B0,10397
fsspec-2025.9.0.dist-info/RECORD,,
fsspec-2025.9.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
fsspec-2025.9.0.dist-info/licenses/LICENSE,sha256=LcNUls5TpzB5FcAIqESq1T53K0mzTN0ARFBnaRQH7JQ,1513
fsspec/__init__.py,sha256=L7qwNBU1iMNQd8Of87HYSNFT9gWlNMSESaJC8fY0AaQ,2053
fsspec/__pycache__/__init__.cpython-310.pyc,,
fsspec/__pycache__/_version.cpython-310.pyc,,
fsspec/__pycache__/archive.cpython-310.pyc,,
fsspec/__pycache__/asyn.cpython-310.pyc,,
fsspec/__pycache__/caching.cpython-310.pyc,,
fsspec/__pycache__/callbacks.cpython-310.pyc,,
fsspec/__pycache__/compression.cpython-310.pyc,,
fsspec/__pycache__/config.cpython-310.pyc,,
fsspec/__pycache__/conftest.cpython-310.pyc,,
fsspec/__pycache__/core.cpython-310.pyc,,
fsspec/__pycache__/dircache.cpython-310.pyc,,
fsspec/__pycache__/exceptions.cpython-310.pyc,,
fsspec/__pycache__/fuse.cpython-310.pyc,,
fsspec/__pycache__/generic.cpython-310.pyc,,
fsspec/__pycache__/gui.cpython-310.pyc,,
fsspec/__pycache__/json.cpython-310.pyc,,
fsspec/__pycache__/mapping.cpython-310.pyc,,
fsspec/__pycache__/parquet.cpython-310.pyc,,
fsspec/__pycache__/registry.cpython-310.pyc,,
fsspec/__pycache__/spec.cpython-310.pyc,,
fsspec/__pycache__/transaction.cpython-310.pyc,,
fsspec/__pycache__/utils.cpython-310.pyc,,
fsspec/_version.py,sha256=LkyV4dUHpfGx8N-SvSE0eARRvdCyg8wWXOl3qM2ZAZ4,710
fsspec/archive.py,sha256=vM6t_lgV6lBWbBYwpm3S4ofBQFQxUPr5KkDQrrQcQro,2411
fsspec/asyn.py,sha256=mE55tO_MmGcxD14cUuaiS3veAqo0h6ZqANfnUuCN3sk,36365
fsspec/caching.py,sha256=86uSgPa5E55b28XEhuC-dMcKAxJtZZnpQqnHTwaF3hI,34294
fsspec/callbacks.py,sha256=BDIwLzK6rr_0V5ch557fSzsivCElpdqhXr5dZ9Te-EE,9210
fsspec/compression.py,sha256=gBK2MV_oTFVW2XDq8bZVbYQKYrl6JDUou6_-kyvmxuk,5086
fsspec/config.py,sha256=LF4Zmu1vhJW7Je9Q-cwkRc3xP7Rhyy7Xnwj26Z6sv2g,4279
fsspec/conftest.py,sha256=fVfx-NLrH_OZS1TIpYNoPzM7efEcMoL62reHOdYeFCA,1245
fsspec/core.py,sha256=1tLctwr7sF1VO3djc_UkjhJ8IAEy0TUMH_bb07Sw17E,23828
fsspec/dircache.py,sha256=YzogWJrhEastHU7vWz-cJiJ7sdtLXFXhEpInGKd4EcM,2717
fsspec/exceptions.py,sha256=pauSLDMxzTJMOjvX1WEUK0cMyFkrFxpWJsyFywav7A8,331
fsspec/fuse.py,sha256=Q-3NOOyLqBfYa4Db5E19z_ZY36zzYHtIs1mOUasItBQ,10177
fsspec/generic.py,sha256=K-b03ifKidHUo99r8nz2pB6oGyf88RtTKahCuBF9ZVU,13409
fsspec/gui.py,sha256=CQ7QsrTpaDlWSLNOpwNoJc7khOcYXIZxmrAJN9bHWQU,14002
fsspec/implementations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fsspec/implementations/__pycache__/__init__.cpython-310.pyc,,
fsspec/implementations/__pycache__/arrow.cpython-310.pyc,,
fsspec/implementations/__pycache__/asyn_wrapper.cpython-310.pyc,,
fsspec/implementations/__pycache__/cache_mapper.cpython-310.pyc,,
fsspec/implementations/__pycache__/cache_metadata.cpython-310.pyc,,
fsspec/implementations/__pycache__/cached.cpython-310.pyc,,
fsspec/implementations/__pycache__/dask.cpython-310.pyc,,
fsspec/implementations/__pycache__/data.cpython-310.pyc,,
fsspec/implementations/__pycache__/dbfs.cpython-310.pyc,,
fsspec/implementations/__pycache__/dirfs.cpython-310.pyc,,
fsspec/implementations/__pycache__/ftp.cpython-310.pyc,,
fsspec/implementations/__pycache__/gist.cpython-310.pyc,,
fsspec/implementations/__pycache__/git.cpython-310.pyc,,
fsspec/implementations/__pycache__/github.cpython-310.pyc,,
fsspec/implementations/__pycache__/http.cpython-310.pyc,,
fsspec/implementations/__pycache__/http_sync.cpython-310.pyc,,
fsspec/implementations/__pycache__/jupyter.cpython-310.pyc,,
fsspec/implementations/__pycache__/libarchive.cpython-310.pyc,,
fsspec/implementations/__pycache__/local.cpython-310.pyc,,
fsspec/implementations/__pycache__/memory.cpython-310.pyc,,
fsspec/implementations/__pycache__/reference.cpython-310.pyc,,
fsspec/implementations/__pycache__/sftp.cpython-310.pyc,,
fsspec/implementations/__pycache__/smb.cpython-310.pyc,,
fsspec/implementations/__pycache__/tar.cpython-310.pyc,,
fsspec/implementations/__pycache__/webhdfs.cpython-310.pyc,,
fsspec/implementations/__pycache__/zip.cpython-310.pyc,,
fsspec/implementations/arrow.py,sha256=721Dikne_lV_0tlgk9jyKmHL6W-5MT0h2LKGvOYQTPI,8623
fsspec/implementations/asyn_wrapper.py,sha256=fox9yjsEu7NCgzdAZJYfNALtUnFkIc_QmeKzaSllZho,3679
fsspec/implementations/cache_mapper.py,sha256=W4wlxyPxZbSp9ItJ0pYRVBMh6bw9eFypgP6kUYuuiI4,2421
fsspec/implementations/cache_metadata.py,sha256=rddh5-0SXIeyWCPpBpOFcaAyWoPyeYmFfeubEWt-nRM,8536
fsspec/implementations/cached.py,sha256=TETvCyf0x-Ak8Y4uiuvIKx2IFYOzvcV0LMUIt4AoJzM,35168
fsspec/implementations/dask.py,sha256=CXZbJzIVOhKV8ILcxuy3bTvcacCueAbyQxmvAkbPkrk,4466
fsspec/implementations/data.py,sha256=LDLczxRh8h7x39Zjrd-GgzdQHr78yYxDlrv2C9Uxb5E,1658
fsspec/implementations/dbfs.py,sha256=1cvvC6KBWOb8pBVpc01xavVbEPXO1xsgZvPD7H73M9k,16217
fsspec/implementations/dirfs.py,sha256=f1sGnQ9Vf0xTxrXo4jDeBy4Qfq3RTqAEemqBSeb0hwY,12108
fsspec/implementations/ftp.py,sha256=bzL_TgH77nMMtTMewRGkbq4iObSHGu7YoMRCXBH4nrc,11639
fsspec/implementations/gist.py,sha256=Ost985hmFr50KsA-QD0shY3hP4KX5qJ9rb5C-X4ehK8,8341
fsspec/implementations/git.py,sha256=qBDWMz5LNllPqVjr5jf_1FuNha4P5lyQI3IlhYg-wUE,3731
fsspec/implementations/github.py,sha256=aCsZL8UvXZgdkcB1RUs3DdLeNrjLKcFsFYeQFDWbBFo,11653
fsspec/implementations/http.py,sha256=f_542L40574onk0tiOsSBABeNq1TXHYUyEBpCeO6vhA,30435
fsspec/implementations/http_sync.py,sha256=UydDqSdUBdhiJ1KufzV8rKGrTftFR4QmNV0safILb8g,30133
fsspec/implementations/jupyter.py,sha256=B2uj7OEm7yIk-vRSsO37_ND0t0EBvn4B-Su43ibN4Pg,3811
fsspec/implementations/libarchive.py,sha256=5_I2DiLXwQ1JC8x-K7jXu-tBwhO9dj7tFLnb0bTnVMQ,7102
fsspec/implementations/local.py,sha256=DQeK7jRGv4_mJAweLKALO5WzIIkjXxZ_jRvwQ_xadSA,16936
fsspec/implementations/memory.py,sha256=Kc6TZSbZ4tdi-6cE5ttEPIgMyq9aAt6cDdVLFRTJvf8,10488
fsspec/implementations/reference.py,sha256=npYj49AmR8rmON9t_BLpfEXqhgsardUeynamqyraOXo,48704
fsspec/implementations/sftp.py,sha256=fMY9XZcmpjszQ2tCqO_TPaJesaeD_Dv7ptYzgUPGoO0,5631
fsspec/implementations/smb.py,sha256=5fhu8h06nOLBPh2c48aT7WBRqh9cEcbIwtyu06wTjec,15236
fsspec/implementations/tar.py,sha256=dam78Tp_CozybNqCY2JYgGBS3Uc9FuJUAT9oB0lolOs,4111
fsspec/implementations/webhdfs.py,sha256=G9wGywj7BkZk4Mu9zXu6HaDlEqX4F8Gw1i4k46CP_-o,16769
fsspec/implementations/zip.py,sha256=9LBMHPft2OutJl2Ft-r9u_z3GptLkc2n91ur2A3bCbg,6072
fsspec/json.py,sha256=3BfNSQ96MB4Xao_ocjheINeqZM2ev7oljUzR5XmNXrE,3814
fsspec/mapping.py,sha256=m2ndB_gtRBXYmNJg0Ie1-BVR75TFleHmIQBzC-yWhjU,8343
fsspec/parquet.py,sha256=6ibAmG527L5JNFS0VO8BDNlxHdA3bVYqdByeiFgpUVM,19448
fsspec/registry.py,sha256=epoYryFFzDWjbkQJfh6xkF3nEu8RTiOzV3-voi8Pshs,12048
fsspec/spec.py,sha256=7cOUe5PC5Uyf56HtGBUHEoym8ktPj-BI8G4HR8Xd_C8,77298
fsspec/tests/abstract/__init__.py,sha256=4xUJrv7gDgc85xAOz1p-V_K1hrsdMWTSa0rviALlJk8,10181
fsspec/tests/abstract/__pycache__/__init__.cpython-310.pyc,,
fsspec/tests/abstract/__pycache__/common.cpython-310.pyc,,
fsspec/tests/abstract/__pycache__/copy.cpython-310.pyc,,
fsspec/tests/abstract/__pycache__/get.cpython-310.pyc,,
fsspec/tests/abstract/__pycache__/mv.cpython-310.pyc,,
fsspec/tests/abstract/__pycache__/open.cpython-310.pyc,,
fsspec/tests/abstract/__pycache__/pipe.cpython-310.pyc,,
fsspec/tests/abstract/__pycache__/put.cpython-310.pyc,,
fsspec/tests/abstract/common.py,sha256=1GQwNo5AONzAnzZj0fWgn8NJPLXALehbsuGxS3FzWVU,4973
fsspec/tests/abstract/copy.py,sha256=gU5-d97U3RSde35Vp4RxPY4rWwL744HiSrJ8IBOp9-8,19967
fsspec/tests/abstract/get.py,sha256=vNR4HztvTR7Cj56AMo7_tx7TeYz1Jgr_2Wb8Lv-UiBY,20755
fsspec/tests/abstract/mv.py,sha256=k8eUEBIrRrGMsBY5OOaDXdGnQUKGwDIfQyduB6YD3Ns,1982
fsspec/tests/abstract/open.py,sha256=Fi2PBPYLbRqysF8cFm0rwnB41kMdQVYjq8cGyDXp3BU,329
fsspec/tests/abstract/pipe.py,sha256=LFzIrLCB5GLXf9rzFKJmE8AdG7LQ_h4bJo70r8FLPqM,402
fsspec/tests/abstract/put.py,sha256=7aih17OKB_IZZh1Mkq1eBDIjobhtMQmI8x-Pw-S_aZk,21201
fsspec/transaction.py,sha256=xliRG6U2Zf3khG4xcw9WiB-yAoqJSHEGK_VjHOdtgo0,2398
fsspec/utils.py,sha256=HC8RFbb7KpEDedsYxExvWvsTObEuUcuuWxd0B_MyGpo,22995
