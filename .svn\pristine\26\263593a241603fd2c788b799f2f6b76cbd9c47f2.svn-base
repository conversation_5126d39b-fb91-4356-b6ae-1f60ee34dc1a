"""
AI Configuration - Simple prompt loader
Chỉ đọc prompt từ file prompts/prompt.txt
"""

from pathlib import Path  # Import thư viện để làm việc với đường dẫn file

def get_available_prompts():
    """
    Trả về thông tin prompt duy nhất từ file prompt.txt
    """
    prompt_file = Path("prompts/prompt.txt")
    if not prompt_file.exists():
        return {}

    # Chỉ trả về thông tin cho prompt.txt
    return {
        'prompt': {
            'file': str(prompt_file),
            'name': 'Dollhouse Miniature',
            'description': 'Style dollhouse miniature với môi trường văn phòng thu nhỏ'
        }
    }

def get_prompt_description(prompt_name):
    """
    Trả về mô tả cho prompt duy nhất
    """
    return 'Style dollhouse miniature với môi trường văn phòng thu nhỏ'

def load_prompt(prompt_name="prompt"):
    """
    Đ<PERSON>c prompt từ file prompts/{prompt_name}.txt
    Trả về prompt thuần túy, không thêm gì cả

    Args:
        prompt_name (str): Tên file prompt (không cần .txt extension)
    """
    prompt_file = Path(f"prompts/{prompt_name}.txt")  # Tạo đường dẫn đến file prompt

    if not prompt_file.exists():  # Kiểm tra file prompt có tồn tại không
        # Fallback to default prompt.txt - Sử dụng file prompt mặc định nếu không tìm thấy
        prompt_file = Path("prompts/prompt.txt")
        if not prompt_file.exists():  # Kiểm tra file mặc định có tồn tại không
            raise FileNotFoundError(f"Prompt file not found: {prompt_file}")  # Báo lỗi nếu không tìm thấy

    try:
        # Mở và đọc nội dung file prompt với encoding UTF-8
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt = f.read().strip()  # Đọc và loại bỏ khoảng trắng thừa

        if not prompt:  # Kiểm tra prompt có nội dung không
            raise ValueError("Prompt file is empty")  # Báo lỗi nếu file rỗng

        # In thông báo đã load prompt thành công
        print(f"✅ Loaded prompt from {prompt_file}: {len(prompt)} characters")
        return prompt  # Trả về nội dung prompt

    except Exception as e:  # Bắt các lỗi khác khi đọc file
        raise Exception(f"Error reading prompt file: {str(e)}")  # Báo lỗi với thông tin chi tiết

def get_gemini_config():
    """
    Trả về config cơ bản cho Gemini 2.0
    """
    return {
        'model': 'gemini-2.0-flash-preview-image-generation',  # Model tạo ảnh
        'fallback_model': 'gemini-2.5-flash'  # Model dự phòng nếu model chính không khả dụng
    }
