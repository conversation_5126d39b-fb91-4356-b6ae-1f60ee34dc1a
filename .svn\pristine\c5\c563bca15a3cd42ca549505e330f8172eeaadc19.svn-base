#!/usr/bin/env python3
"""
Script để tải font Noto Sans thông thường hỗ trợ tiếng Việt
"""

import requests
import os
from pathlib import Path

def download_noto_sans_fonts():
    """Tải font Noto Sans thông thường"""
    
    # Tạo thư mục fonts nếu chưa có
    fonts_dir = Path("fonts")
    fonts_dir.mkdir(exist_ok=True)
    
    # URLs cho font Noto Sans thông thường
    font_urls = {
        "NotoSans-Regular.ttf": "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Regular.ttf",
        "NotoSans-Bold.ttf": "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSans/NotoSans-Bold.ttf",
    }
    
    print("🔄 Đang tải font Noto Sans thông thường...")
    
    for filename, url in font_urls.items():
        file_path = fonts_dir / filename
        
        if file_path.exists():
            print(f"✅ {filename} đã tồn tại")
            continue
            
        try:
            print(f"📥 Đang tải {filename}...")
            response = requests.get(url, timeout=60)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Đã tải {filename} ({len(response.content)} bytes)")
            
        except Exception as e:
            print(f"❌ Lỗi khi tải {filename}: {e}")
    
    print("🎉 Hoàn thành tải font Noto Sans!")
    print("📝 Font này hỗ trợ tiếng Việt và nhiều ngôn ngữ khác")

if __name__ == "__main__":
    download_noto_sans_fonts()
