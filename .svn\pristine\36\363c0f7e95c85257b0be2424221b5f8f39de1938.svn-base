"""
Camera Controller - <PERSON><PERSON> thống Camera Hiệu suất Cao
High Performance Camera System
Dựa trên triển khai tham chiếu project_directory
Based on project_directory reference implementation
"""

import cv2  # Thư viện OpenCV để xử lý camera và ảnh
from flask import Blueprint, Response, jsonify, request  # Flask components
import threading  # Thư viện đa luồng để xử lý camera song song
import time  # Thư viện thời gian
import os  # Thư viện hệ điều hành
from pathlib import Path  # Thư viện xử lý đường dẫn
from datetime import datetime  # Thư viện ngày tháng
import subprocess  # Thư viện subprocess để chạy lệnh hệ thống
import json  # Thư viện JSON để xử lý cấu hình
import platform  # Thư viện platform để phát hiện hệ điều hành

from models.session_model import SessionModel  # Model quản lý session
from config import CameraConfig  # <PERSON><PERSON><PERSON> hình camera
from utils.path_manager import path_manager  # Path manager để xử lý đường dẫn tập trung


class CameraController:
    """Controller camera hiệu suất cao dựa trên project_directory - High-performance camera controller based on project_directory"""

    def __init__(self):
        """Khởi tạo Camera Controller"""
        self.blueprint = Blueprint('camera', __name__)  # Tạo Flask Blueprint cho camera
        self.setup_routes()  # Thiết lập các routes

        # Thuộc tính camera cốt lõi (CHÍNH XÁC theo kiểu project_directory) - Core camera attributes (EXACT project_directory style)
        self.camera_0 = None  # Camera name card (Logitech bên trái) - Business card camera (Logitech bên trái)
        self.camera_1 = None  # Camera khuôn mặt (Laptop bên phải) - Face camera (Laptop bên phải)
        self.output_frame_0 = None  # Frame mới nhất từ camera 0 - Latest frame from camera 0
        self.output_frame_1 = None  # Frame mới nhất từ camera 1 - Latest frame from camera 1
        self.lock_0 = threading.Lock()  # Khóa luồng cho camera 0 - Thread lock for camera 0
        self.lock_1 = threading.Lock()  # Khóa luồng cho camera 1 - Thread lock for camera 1
        self.initial_focus_value = 0  # Focus thủ công cho camera Logitech - Manual focus for Logitech camera

        # Thuộc tính theo dõi bổ sung - Additional tracking attributes
        self.active_streams = set()  # Tập hợp các stream đang hoạt động

        # Camera stream control - NEW FUNCTIONALITY FOR CAPTURE WORKFLOW
        self.streaming_active_0 = True  # Camera 0 streaming state
        self.streaming_active_1 = True  # Camera 1 streaming state
        self.capture_mode_0 = False  # Camera 0 in capture mode (paused streaming)
        self.capture_mode_1 = False  # Camera 1 in capture mode (paused streaming)

        # Phát hiện môi trường - Environment detection
        self.is_server_environment = self.detect_server_environment()  # Kiểm tra có phải môi trường server không
        self.webrtc_only_mode = False  # Cờ cho chế độ chỉ WebRTC - Flag for WebRTC-only operation

        # Smart Camera Detection - NEW FEATURE
        self.smart_camera_mapping = {}  # Mapping thông minh camera
        self.camera_config_file = "camera_config.json"  # File cấu hình camera

        # Khởi tạo camera và bắt đầu streaming - Initialize cameras and start streaming
        try:
            self.setup_cameras()  # Thiết lập camera
        except KeyboardInterrupt:
            print("⚠️ Camera setup interrupted by user")
            print("🔄 Continuing with WebRTC-only mode...")
            self.webrtc_only_mode = True
        except Exception as e:
            print(f"❌ Camera setup error: {e}")
            print("🔄 Falling back to WebRTC-only mode...")
            self.webrtc_only_mode = True

        self.session_model = SessionModel()  # Khởi tạo model session
        print(f"Camera Controller initialized - Server Environment: {self.is_server_environment}")
        print("✅ Camera stream pause/resume functionality is working!")

    def setup_routes(self):
        """Thiết lập các routes camera (CHÍNH XÁC theo kiểu project_directory) - Setup camera routes (EXACT project_directory style)"""
        # Route để stream video từ camera
        self.blueprint.add_url_rule('/video_feed/<int:camera_id>', 'video_feed', self.video_feed)
        # Route để chụp ảnh từ camera
        self.blueprint.add_url_rule('/capture_step/<int:camera_id>', 'capture_step', self.capture_step, methods=['POST'])
        # Face detection stream route
        self.blueprint.add_url_rule('/face_detection_feed', 'face_detection_feed', self.face_detection_feed)
        # Alternative route name for compatibility
        self.blueprint.add_url_rule('/face_detect_feed', 'face_detect_feed', self.face_detection_feed)
        # Card detection stream route
        self.blueprint.add_url_rule('/card_detection_feed', 'card_detection_feed', self.card_detection_feed)
        # Auto capture route for face detection
        self.blueprint.add_url_rule('/auto_capture', 'auto_capture', self.auto_capture, methods=['POST'])
        # Route để upload ảnh từ client
        self.blueprint.add_url_rule('/upload_camera_image', 'upload_camera_image', self.upload_camera_image, methods=['POST'])
        # Route lấy trạng thái session
        self.blueprint.add_url_rule('/session_status', 'session_status', self.session_status)
        # Route lấy session_id hiện tại cho auto capture
        self.blueprint.add_url_rule('/current_session_id', 'get_current_session_id', self.get_current_session_id, methods=['GET'])
        # NEW ROUTES: Camera stream control
        self.blueprint.add_url_rule('/pause_stream/<int:camera_id>', 'pause_stream', self.pause_stream, methods=['POST'])
        self.blueprint.add_url_rule('/resume_stream/<int:camera_id>', 'resume_stream', self.resume_stream, methods=['POST'])
        self.blueprint.add_url_rule('/stop_all_streams', 'stop_all_streams', self.stop_all_streams, methods=['POST'])
        # TEST ROUTE: Manual camera control for testing
        self.blueprint.add_url_rule('/test_camera_control/<int:camera_id>/<action>', 'test_camera_control', self.test_camera_control, methods=['GET'])
        # Route lấy trạng thái camera
        self.blueprint.add_url_rule('/camera_status', 'camera_status_api', self.camera_status_api, methods=['GET'])
        # SIMPLE TEST ROUTE
        self.blueprint.add_url_rule('/test_simple', 'test_simple', self.test_simple, methods=['GET'])
        # Route điều chỉnh focus camera
        self.blueprint.add_url_rule('/adjust_focus', 'adjust_focus', self.adjust_focus, methods=['POST'])
        # Route reset face camera
        self.blueprint.add_url_rule('/reset_face_camera', 'reset_face_camera', self.reset_face_camera_api, methods=['POST'])
        # Route reset card camera
        self.blueprint.add_url_rule('/reset_card_camera', 'reset_card_camera', self.reset_card_camera_api, methods=['POST'])

    def detect_server_environment(self):
        """Phát hiện nếu đang chạy trong môi trường server (không có camera vật lý) - Detect if running in server environment (no physical cameras)"""
        import platform  # Import để kiểm tra hệ điều hành
        import subprocess  # Import để chạy lệnh hệ thống

        try:
            # Kiểm tra các chỉ báo server phổ biến - Check for common server indicators
            system = platform.system().lower()  # Lấy tên hệ điều hành

            # Kiểm tra nếu đang chạy trong Docker/container - Check if running in Docker/container
            if os.path.exists('/.dockerenv'):  # File này tồn tại trong Docker container
                print("🐳 Docker environment detected - WebRTC mode")
                return True  # Trả về True nếu là Docker

            # Kiểm tra môi trường headless (không có display) - Check for headless environment (no display)
            if system == 'linux':  # Nếu là Linux
                display = os.environ.get('DISPLAY')  # Lấy biến DISPLAY
                if not display:  # Nếu không có DISPLAY
                    print("🖥️ Headless Linux environment detected - WebRTC mode")
                    return True  # Trả về True nếu là headless

            # Kiểm tra các chỉ báo cloud server - Check for cloud server indicators
            cloud_indicators = [
                'AWS_EXECUTION_ENV',  # AWS Lambda/EC2
                'GOOGLE_CLOUD_PROJECT',  # Google Cloud
                'AZURE_CLIENT_ID',  # Microsoft Azure
                'HEROKU_APP_NAME'  # Heroku
            ]

            for indicator in cloud_indicators:  # Lặp qua từng chỉ báo
                if os.environ.get(indicator):  # Nếu biến môi trường tồn tại
                    print(f"☁️ Cloud environment detected ({indicator}) - WebRTC mode")
                    return True  # Trả về True nếu là cloud

            # Thử phát hiện camera có sẵn (kiểm tra nhanh) - Try to detect available cameras (quick check)
            if system == 'linux':  # Nếu là Linux
                try:
                    # Chạy lệnh ls để kiểm tra thiết bị video
                    result = subprocess.run(['ls', '/dev/video*'],
                                          capture_output=True, text=True, timeout=2)
                    if result.returncode != 0:  # Nếu không tìm thấy thiết bị video
                        print("📹 No video devices found - WebRTC mode")
                        return True  # Trả về True nếu không có camera
                except:  # Nếu có lỗi khi kiểm tra
                    print("📹 Cannot check video devices - WebRTC mode")
                    return True  # Trả về True nếu không kiểm tra được

            print("💻 Local environment detected - Hybrid mode")  # Môi trường local
            return False  # Trả về False nếu là môi trường local

        except Exception as e:
            print(f"⚠️ Environment detection failed: {e} - Defaulting to WebRTC mode")  # Log lỗi
            return True  # Mặc định là server environment

    def load_camera_mapping(self):
        """Tải cấu hình camera mapping từ file"""
        try:
            if os.path.exists(self.camera_mapping_file):
                with open(self.camera_mapping_file, 'r', encoding='utf-8') as f:
                    mapping = json.load(f)
                    return mapping
            return None
        except Exception as e:
            print(f"⚠️ Lỗi tải camera mapping: {e}")
            return None

    def get_windows_camera_devices(self):
        """Lấy danh sách camera devices từ Windows - Get camera devices from Windows"""
        devices = []

        try:
            cmd = [
                "powershell", "-Command",
                """
                Get-WmiObject -Class Win32_PnPEntity | Where-Object {
                    ($_.Name -like '*camera*' -or
                     $_.Name -like '*webcam*' -or
                     $_.Name -like '*video*' -or
                     $_.Name -like '*Razer*' -or
                     $_.Name -like '*Kiyo*' -or
                     $_.Name -like '*Logi*') -and
                    ($_.Name -notlike '*microphone*' -and
                     $_.Name -notlike '*audio*')
                } | Select-Object Name, DeviceID, Status, Manufacturer |
                ConvertTo-Json -Depth 3
                """
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)

            if result.returncode == 0 and result.stdout.strip():
                data = json.loads(result.stdout)
                if not isinstance(data, list):
                    data = [data]
                devices = data

        except Exception as e:
            print(f"⚠️ Lỗi khi lấy thông tin Windows devices: {e}")

        return devices

    def classify_camera_type(self, device_name, device_id):
        """Phân loại camera theo tên và device ID - Classify camera by name and device ID"""
        name_lower = device_name.lower()
        id_lower = device_id.lower()

        # USB External cameras (ưu tiên cho Business Card)
        if any(keyword in name_lower for keyword in ['razer', 'kiyo', 'logitech', 'logi']):
            if 'razer' in name_lower or 'kiyo' in name_lower:
                return 'usb_razer'
            elif 'logi' in name_lower:
                return 'usb_logitech'
            else:
                return 'usb_external'

        # Built-in cameras (ưu tiên cho Face)
        if any(keyword in name_lower for keyword in ['integrated', 'built', 'internal']):
            return 'builtin'

        # Phân tích theo Device ID
        if 'usb\\vid_' in id_lower:
            # Razer Vendor ID: 1532
            if 'vid_1532' in id_lower:
                return 'usb_razer'
            # Logitech Vendor ID: 046D
            elif 'vid_046d' in id_lower:
                return 'usb_logitech'
            else:
                return 'usb_external'

        return 'unknown'

    def smart_camera_detection(self):
        """Smart camera detection và mapping - Smart camera detection and mapping"""
        print("🧠 Smart Camera Detection...")

        # Lấy thông tin devices từ Windows
        if platform.system().lower() == 'windows':
            camera_devices = self.get_windows_camera_devices()
            print(f"📋 Tìm thấy {len(camera_devices)} camera devices")

            # Phân loại devices
            device_types = {}
            razer_found = False

            for device in camera_devices:
                name = device.get('Name', 'Unknown')
                device_id = device.get('DeviceID', '')
                camera_type = self.classify_camera_type(name, device_id)
                device_types[name] = camera_type

                if camera_type == 'usb_razer':
                    razer_found = True
                    print(f"🎯 Tìm thấy Razer Kiyo: {name}")

            if razer_found:
                print("✅ Razer Kiyo được phát hiện - sẽ ưu tiên cho Business Card")

        # Test các camera index để tìm mapping tối ưu
        working_cameras = []
        for index in range(4):  # Test 4 camera đầu
            if self._test_camera_index(index):
                working_cameras.append(index)

        print(f"📊 Tìm thấy {len(working_cameras)} camera hoạt động: {working_cameras}")

        # Intelligent mapping với phân tích properties
        if len(working_cameras) >= 2:
            # Phân tích properties để xác định camera USB vs Built-in
            camera_scores = {}

            for index in working_cameras[:2]:  # Chỉ phân tích 2 camera đầu
                score = self._analyze_camera_properties(index)
                camera_scores[index] = score
                print(f"📊 Camera {index} analysis score: {score}")

            # Sắp xếp theo score (score cao hơn = USB camera)
            sorted_cameras = sorted(camera_scores.items(), key=lambda x: x[1], reverse=True)

            if len(sorted_cameras) >= 2:
                # Camera có score cao nhất = USB = Business Card
                # Camera có score thấp nhất = Built-in = Face
                usb_camera = sorted_cameras[0][0]  # Highest score
                builtin_camera = sorted_cameras[1][0]  # Lower score

                self.smart_camera_mapping = {
                    'business_card': usb_camera,
                    'face': builtin_camera
                }

                print(f"🎯 Smart mapping: Business Card = Camera {usb_camera} (USB), Face = Camera {builtin_camera} (Built-in)")
                return True
            else:
                # Fallback nếu không phân tích được
                self.smart_camera_mapping = {
                    'business_card': working_cameras[0],
                    'face': working_cameras[1]
                }
                print(f"🎯 Fallback mapping: Business Card = Camera {working_cameras[0]}, Face = Camera {working_cameras[1]}")
                return True

        else:
            print("⚠️ Không đủ camera để smart mapping - sử dụng mapping mặc định")
            return False

    def _analyze_camera_properties(self, index):
        """Phân tích properties của camera để xác định loại (USB vs Built-in)"""
        try:
            cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)
            time.sleep(0.5)

            if not cap.isOpened():
                cap = cv2.VideoCapture(index)
                time.sleep(0.5)

            if not cap.isOpened():
                return 0  # Không mở được

            # Đọc frame để test
            ret, frame = cap.read()
            if not ret or frame is None:
                cap.release()
                return 0

            # Lấy properties
            properties = {
                'brightness': cap.get(cv2.CAP_PROP_BRIGHTNESS),
                'contrast': cap.get(cv2.CAP_PROP_CONTRAST),
                'saturation': cap.get(cv2.CAP_PROP_SATURATION),
                'gain': cap.get(cv2.CAP_PROP_GAIN),
                'exposure': cap.get(cv2.CAP_PROP_EXPOSURE),
            }

            cap.release()

            # Tính score dựa trên properties
            score = 0

            # USB cameras thường có brightness cao hơn
            if properties['brightness'] > 100:
                score += 2

            # USB cameras thường có contrast cao hơn
            if properties['contrast'] > 32:
                score += 2

            # USB cameras thường có gain control
            if properties['gain'] != -1 and properties['gain'] > 0:
                score += 3

            # USB cameras thường có exposure control tốt hơn
            if properties['exposure'] != -1:
                score += 1

            # Frame quality analysis
            if frame is not None:
                mean_brightness = frame.mean()
                if mean_brightness > 100:
                    score += 1

            return score

        except Exception as e:
            print(f"⚠️ Lỗi phân tích camera {index}: {e}")
            return 0

    def _test_camera_index(self, index):
        """Test camera tại index cụ thể - Test camera at specific index"""
        try:
            cap = cv2.VideoCapture(index, cv2.CAP_DSHOW)
            if not cap.isOpened():
                cap = cv2.VideoCapture(index)

            if cap.isOpened():
                ret, frame = cap.read()
                success = ret and frame is not None
                cap.release()
                return success

        except Exception:
            pass

        return False

    def setup_cameras(self):
        """Thiết lập và khởi tạo camera với nhận biết môi trường server - Setup and initialize cameras with server environment awareness"""
        print("🎥 Setting up cameras...")  # Log bắt đầu thiết lập camera

        try:
            if self.is_server_environment:  # Nếu là môi trường server
                print("🌐 Server environment detected - Skipping physical camera initialization")  # Log phát hiện server
                print("📱 WebRTC-only mode enabled - Clients will use their own cameras")  # Log chế độ WebRTC
                self.webrtc_only_mode = True  # Bật chế độ chỉ WebRTC

                # Tạo frame giả để tương thích - Create dummy frames for compatibility
                import numpy as np  # Import numpy để tạo array
                dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)  # Tạo frame đen 480x640
                with self.lock_0:  # Khóa luồng camera 0
                    self.output_frame_0 = dummy_frame  # Đặt frame giả cho camera 0
                with self.lock_1:  # Khóa luồng camera 1
                    self.output_frame_1 = dummy_frame  # Đặt frame giả cho camera 1

                print("✅ Server camera system ready (WebRTC-only mode)")  # Log hệ thống sẵn sàng
                return True  # Trả về thành công

            # Môi trường local - thử khởi tạo camera vật lý - Local environment - try to initialize physical cameras
            print("💻 Local environment - Attempting physical camera initialization")  # Log thử khởi tạo camera
            try:
                success = self.initialize_cameras()  # Gọi hàm khởi tạo camera
            except KeyboardInterrupt:
                print("⚠️ Camera initialization interrupted by user")
                print("🔄 Falling back to WebRTC-only mode...")
                self.webrtc_only_mode = True
                print("✅ WebRTC-only mode activated")
                return True
            except Exception as e:
                print(f"❌ Camera initialization error: {e}")
                success = False

            if success:  # Nếu khởi tạo thành công
                # Bắt đầu các luồng streaming - Start streaming threads
                self.start_streaming_threads()  # Khởi động luồng streaming
                print("✅ Physical camera system ready")  # Log hệ thống camera sẵn sàng
                return True  # Trả về thành công
            else:  # Nếu khởi tạo thất bại
                print("⚠️ Physical cameras failed - Falling back to WebRTC-only mode")  # Log fallback
                self.webrtc_only_mode = True  # Chuyển sang chế độ WebRTC

                # Tạo frame giả để tương thích - Create dummy frames for compatibility
                import numpy as np  # Import numpy
                dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)  # Tạo frame đen
                with self.lock_0:  # Khóa luồng camera 0
                    self.output_frame_0 = dummy_frame  # Đặt frame giả
                with self.lock_1:  # Khóa luồng camera 1
                    self.output_frame_1 = dummy_frame  # Đặt frame giả

                return True  # Trả về thành công

        except Exception as e:
            print(f"⚠️ Camera setup error: {e} - Enabling WebRTC-only mode")  # Log lỗi thiết lập
            self.webrtc_only_mode = True  # Bật chế độ WebRTC
            return True  # Trả về thành công

    def start_streaming_threads(self):
        """Khởi động các luồng streaming camera - Start camera streaming threads"""
        try:
            # Khởi động luồng cho camera 0 (Name Card) - Start thread for camera 0 (Business Card)
            if self.camera_0 and self.camera_0.isOpened():  # Nếu camera 0 mở được
                threading.Thread(  # Tạo luồng mới
                    target=lambda: list(self.generate_frames(0)),  # Hàm target tạo frame
                    daemon=True,  # Luồng daemon
                    name="Camera0Thread"  # Tên luồng
                ).start()  # Khởi động luồng
                print("🎬 Started streaming thread for Camera 0 (Business Card)")  # Log khởi động luồng

            # Khởi động luồng cho camera 1 (Khuôn mặt) - Start thread for camera 1 (Face)
            if self.camera_1 and self.camera_1.isOpened():  # Nếu camera 1 mở được
                threading.Thread(  # Tạo luồng mới
                    target=lambda: list(self.generate_frames(1)),  # Hàm target tạo frame cho camera 1
                    daemon=True,  # Luồng daemon
                    name="Camera1Thread"  # Tên luồng
                ).start()  # Khởi động luồng
                print("🎬 Started streaming thread for Camera 1 (Face)")  # Log khởi động luồng camera 1

        except Exception as e:
            print(f"❌ Error starting streaming threads: {e}")  # Log lỗi khởi động luồng

    def initialize_cameras(self):
        """Khởi tạo camera với force mapping cho camera thứ 3 - Initialize cameras with force mapping for 3rd camera"""
        print("🎥 Initializing cameras with FORCE MAPPING for camera thứ 3...")

        try:
            # FORCE MAPPING: Thử sử dụng camera thứ 3 trực tiếp
            force_mapping = self._try_force_camera_mapping()
        except KeyboardInterrupt:
            print("⚠️ Camera initialization interrupted by user")
            print("🔄 Falling back to default camera mapping...")
            force_mapping = None

        try:

            if force_mapping:
                print("🎯 Using FORCE MAPPING for camera thứ 3!")
                business_card_index = force_mapping['business_card']
                face_index = force_mapping['face']
            else:
                # Tải cấu hình camera mapping
                mapping = self.load_camera_mapping()

                if mapping:
                    # Sử dụng mapping từ file cấu hình
                    business_card_index = mapping.get('business_card_camera', 0)
                    face_index = mapping.get('face_camera', 1)

                    print(f"📋 Using saved camera mapping:")
                    print(f"   📄 Business Card: Camera {business_card_index}")
                    print(f"   👤 Face: Camera {face_index}")
                else:
                    # Fallback về mapping mặc định
                    print("⚠️ No camera mapping found - using default mapping")
                    print("💡 Run 'python camera_config_simple.py' to configure cameras")
                    business_card_index = 0
                    face_index = 1

            # Khởi tạo camera với mapping
            print(f"📄 Initializing Camera {business_card_index} for Business Card...")
            self.camera_0 = self._initialize_single_camera(business_card_index, "Business Card")

            print(f"👤 Initializing Camera {face_index} for Face...")
            self.camera_1 = self._initialize_single_camera(face_index, "Face")

            # Log trạng thái cuối cùng - Log final status
            cam0_ok = self.camera_0 is not None and self.camera_0.isOpened()  # Kiểm tra camera 0 OK
            cam1_ok = self.camera_1 is not None and self.camera_1.isOpened()  # Kiểm tra camera 1 OK

            print(f"📊 Camera initialization complete:")  # Log hoàn thành khởi tạo
            print(f"   📄 Business Card Camera (Index {business_card_index}): {'✅ OK' if cam0_ok else '❌ FAILED'}")
            print(f"   👤 Face Camera (Index {face_index}): {'✅ OK' if cam1_ok else '❌ FAILED'}")

            if force_mapping and cam1_ok:
                print(f"🎉 SUCCESS! Camera thứ 3 (Index {face_index}) is working!")

            return cam0_ok or cam1_ok  # Trả về True nếu ít nhất 1 camera hoạt động - Return True if at least one camera works

        except Exception as e:
            print(f"❌ Camera initialization error: {e}")  # Log lỗi khởi tạo camera
            return False  # Trả về False nếu có lỗi

    def _try_force_camera_mapping(self):
        """USB Camera Priority Detection - Ưu tiên USB cameras trước laptop camera"""
        print("🎯 USB CAMERA PRIORITY DETECTION")
        print("   Logic: USB cameras → Laptop camera (fallback)")

        # Scan tất cả camera có sẵn
        available_cameras = []
        try:
            for i in range(6):  # Test 6 camera đầu
                print(f"   🔍 Testing camera {i}...")
                if self._test_single_camera_fast(i):
                    available_cameras.append(i)
                    print(f"   ✅ Found working camera at index {i}")
                else:
                    print(f"   ❌ Camera {i} not available")
        except KeyboardInterrupt:
            print("⚠️ Camera scanning interrupted by user")
            if len(available_cameras) == 0:
                print("   ❌ No cameras found before interruption")
                return None
            print(f"   📊 Found {len(available_cameras)} cameras before interruption: {available_cameras}")

        print(f"📊 Total available cameras: {available_cameras}")

        if len(available_cameras) == 0:
            print("   ❌ No cameras found")
            return None

        # Phân loại camera theo loại (USB vs Laptop)
        usb_cameras, laptop_cameras = self._classify_cameras_by_type(available_cameras)

        print(f"📱 USB cameras: {usb_cameras}")
        print(f"💻 Laptop cameras: {laptop_cameras}")

        # Áp dụng logic ưu tiên mới
        return self._apply_usb_priority_logic(usb_cameras, laptop_cameras)

    def _classify_cameras_by_type(self, available_cameras):
        """Phân loại camera thành USB và Laptop cameras"""
        usb_cameras = []
        laptop_cameras = []

        for camera_index in available_cameras:
            camera_type = self._detect_camera_type(camera_index)

            if camera_type == 'usb':
                usb_cameras.append(camera_index)
                print(f"   📱 Camera {camera_index}: USB camera")
            else:
                laptop_cameras.append(camera_index)
                print(f"   💻 Camera {camera_index}: Laptop camera")

        return usb_cameras, laptop_cameras

    def _detect_camera_type(self, camera_index):
        """Detect camera type dựa trên properties và heuristics"""
        try:
            cap = cv2.VideoCapture(camera_index, cv2.CAP_DSHOW)
            time.sleep(0.3)

            if not cap.isOpened():
                cap.release()
                return 'unknown'

            # Đọc frame để phân tích
            ret, frame = cap.read()
            if not ret or frame is None:
                cap.release()
                return 'unknown'

            # Lấy properties để phân tích
            properties = {
                'brightness': cap.get(cv2.CAP_PROP_BRIGHTNESS),
                'contrast': cap.get(cv2.CAP_PROP_CONTRAST),
                'saturation': cap.get(cv2.CAP_PROP_SATURATION),
                'gain': cap.get(cv2.CAP_PROP_GAIN),
                'exposure': cap.get(cv2.CAP_PROP_EXPOSURE),
                'width': cap.get(cv2.CAP_PROP_FRAME_WIDTH),
                'height': cap.get(cv2.CAP_PROP_FRAME_HEIGHT),
            }

            cap.release()

            # USB camera detection heuristics
            usb_score = 0

            # USB cameras thường có brightness/contrast cao hơn
            if properties['brightness'] > 100:
                usb_score += 2
            if properties['contrast'] > 32:
                usb_score += 2

            # USB cameras thường có gain control
            if properties['gain'] != -1 and properties['gain'] > 0:
                usb_score += 3

            # USB cameras thường có exposure control tốt hơn
            if properties['exposure'] != -1:
                usb_score += 1

            # USB cameras thường có resolution cao hơn hoặc khác biệt
            if properties['width'] >= 1280 or properties['height'] >= 720:
                usb_score += 2

            # Camera index cao hơn thường là USB (heuristic)
            if camera_index >= 1:
                usb_score += 1

            # Threshold để quyết định
            return 'usb' if usb_score >= 4 else 'laptop'

        except Exception as e:
            print(f"   ⚠️ Error detecting camera {camera_index} type: {e}")
            return 'unknown'

    def _apply_usb_priority_logic(self, usb_cameras, laptop_cameras):
        """Áp dụng logic ưu tiên USB cameras"""
        print(f"\n🎯 APPLYING USB PRIORITY LOGIC")
        print(f"   USB cameras available: {len(usb_cameras)}")
        print(f"   Laptop cameras available: {len(laptop_cameras)}")

        # Case 1: Có ≥2 USB cameras - Dùng 2 USB cameras
        if len(usb_cameras) >= 2:
            mapping = {
                'business_card': usb_cameras[0],
                'face': usb_cameras[1]
            }
            print(f"✅ Case 1: Using 2 USB cameras")
            print(f"   📄 Business Card: USB Camera {mapping['business_card']}")
            print(f"   👤 Face: USB Camera {mapping['face']}")
            return mapping

        # Case 2: Có 1 USB camera - USB cho Business Card, laptop cho Face
        elif len(usb_cameras) == 1 and len(laptop_cameras) >= 1:
            mapping = {
                'business_card': usb_cameras[0],
                'face': laptop_cameras[0]
            }
            print(f"✅ Case 2: USB + Laptop combination")
            print(f"   📄 Business Card: USB Camera {mapping['business_card']}")
            print(f"   👤 Face: Laptop Camera {mapping['face']}")
            return mapping

        # Case 3: Có 0 USB camera - Dùng laptop camera cho cả 2
        elif len(laptop_cameras) >= 1:
            if len(laptop_cameras) >= 2:
                mapping = {
                    'business_card': laptop_cameras[0],
                    'face': laptop_cameras[1]
                }
                print(f"✅ Case 3a: Using 2 laptop cameras")
            else:
                mapping = {
                    'business_card': laptop_cameras[0],
                    'face': laptop_cameras[0]
                }
                print(f"✅ Case 3b: Using 1 laptop camera for both")

            print(f"   📄 Business Card: Laptop Camera {mapping['business_card']}")
            print(f"   👤 Face: Laptop Camera {mapping['face']}")
            return mapping

        # Case 4: Fallback - không có camera phù hợp
        else:
            print(f"❌ No suitable camera combination found")
            return None



    def _test_single_camera_fast(self, camera_id):
        """Test camera nhanh với minimal error output"""
        try:
            import cv2
            import time
            import signal

            # Chỉ test với DirectShow - backend ổn định nhất
            cap = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)
            time.sleep(0.3)

            if cap.isOpened():
                # Thêm timeout để tránh bị treo
                try:
                    # Set timeout cho camera read
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    ret, frame = cap.read()
                    success = ret and frame is not None
                except KeyboardInterrupt:
                    print(f"⚠️ Camera {camera_id} test interrupted by user")
                    success = False
                except Exception as e:
                    print(f"⚠️ Camera {camera_id} read error: {e}")
                    success = False
                finally:
                    cap.release()
                return success

            cap.release()
            return False

        except KeyboardInterrupt:
            print(f"⚠️ Camera {camera_id} initialization interrupted by user")
            return False
        except Exception as e:
            print(f"⚠️ Camera {camera_id} test error: {e}")
            return False

    def _initialize_single_camera(self, camera_id, camera_name):
        """Khởi tạo một camera đơn với xử lý lỗi phù hợp - Initialize a single camera with proper error handling"""
        try:
            import cv2  # Import OpenCV
            import time  # Import thư viện time

            print(f"🔧 Trying to open {camera_name} (ID: {camera_id})...")  # Log thử mở camera

            # Thử CAP_DSHOW trước (tốt nhất cho Windows) - Try CAP_DSHOW first (best for Windows)
            cap = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)  # Tạo VideoCapture với CAP_DSHOW
            time.sleep(0.5)  # Cho camera khởi động - Allow camera to warm up

            if not cap.isOpened():  # Nếu CAP_DSHOW thất bại
                print(f"⚠️ CAP_DSHOW failed, trying default...")  # Log CAP_DSHOW thất bại
                cap = cv2.VideoCapture(camera_id)  # Thử với backend mặc định
                time.sleep(0.5)  # Cho camera khởi động

            if cap.isOpened():  # Nếu camera mở được
                # Test xem có thể đọc frame không - Test if we can read a frame
                ret, frame = cap.read()  # Đọc frame test
                if ret and frame is not None:  # Nếu đọc frame thành công
                    print(f"✅ {camera_name} initialized successfully")  # Log khởi tạo thành công

                    # Đặt thiết lập tối ưu - Set optimal settings
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)  # Đặt độ rộng frame
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)  # Đặt độ cao frame
                    cap.set(cv2.CAP_PROP_FPS, 30)  # Đặt FPS

                    # Đặt focus cho camera 0 nếu cần - Set focus for camera 0 if needed
                    if camera_id == 0:  # Nếu là camera 0
                        try:
                            cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
                            cap.set(cv2.CAP_PROP_FOCUS, self.initial_focus_value)
                        except:
                            pass  # Focus setting might not be supported
                    
                    return cap
                else:
                    cap.release()
            
            print(f"❌ Failed to initialize {camera_name}")
            return None
            
        except Exception as e:
            print(f"❌ Error initializing {camera_name}: {e}")
            return None
    
    def generate_frames(self, camera_id):
        """Generate frames for video streaming"""
        frame_count = 0
        start_time = time.time()
        
        frame_count = 0
        while True:
            try:
                frame_count += 1

                # Check if streaming is active for this camera
                streaming_active = self.streaming_active_0 if camera_id == 0 else self.streaming_active_1
                capture_mode = self.capture_mode_0 if camera_id == 0 else self.capture_mode_1

                # DEBUG: Log stream state every 100 frames (reduced logging)
                # if frame_count % 100 == 0:
                #     print(f"🔍 Camera {camera_id} Frame {frame_count}: streaming_active={streaming_active}, capture_mode={capture_mode}")

                # If streaming is paused or in capture mode, STOP COMPLETELY
                if not streaming_active or capture_mode:
                    print(f"🛑 Camera {camera_id} STOPPED - No frames will be sent")
                    time.sleep(1.0)  # Just wait, don't send any frames
                    continue

                cap = self.camera_0 if camera_id == 0 else self.camera_1
                lock = self.lock_0 if camera_id == 0 else self.lock_1

                if cap is None or not cap.isOpened():
                    print(f"Camera {camera_id} not ready, waiting...")
                    time.sleep(2)
                    continue
                
                ret, frame = cap.read()
                if not ret or frame is None:
                    print(f"Camera {camera_id} read failed, reinitializing...")
                    self._reinitialize_camera(camera_id)
                    time.sleep(1)
                    continue
                
                # Store frame for capture
                with lock:
                    if camera_id == 0:
                        self.output_frame_0 = frame.copy()
                    else:
                        self.output_frame_1 = frame.copy()
                
                # FPS monitoring (every second) - DISABLED FOR DEBUG
                # frame_count += 1
                # elapsed = time.time() - start_time
                # if elapsed >= 1.0:
                #     fps = frame_count / elapsed
                #     print(f"[Camera {camera_id}] FPS: {fps:.1f}")
                #     frame_count = 0
                #     start_time = time.time()
                
                # Encode frame for streaming
                ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if not ret:
                    continue
                    
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                       
            except Exception as e:
                print(f"❌ Error in generate_frames for camera {camera_id}: {e}")
                time.sleep(1)

    def _reinitialize_camera(self, camera_id):
        """Reinitialize a specific camera"""
        try:
            if camera_id == 0:
                with self.lock_0:
                    if self.camera_0:
                        self.camera_0.release()
                    self.camera_0 = self._initialize_single_camera(0, "Business Card")
            else:
                with self.lock_1:
                    if self.camera_1:
                        self.camera_1.release()
                    self.camera_1 = self._initialize_single_camera(1, "Face")
                
        except Exception as e:
            print(f"❌ Error reinitializing camera {camera_id}: {e}")

    def video_feed(self, camera_id):
        """Video streaming route with WebRTC-only mode support and card detection for camera 0"""
        if self.webrtc_only_mode:
            # Return placeholder image for WebRTC-only mode
            return Response(self.generate_placeholder_frames(camera_id),
                           mimetype='multipart/x-mixed-replace; boundary=frame')

        # Sử dụng card detection cho camera 0 (Business Card)
        if camera_id == 0:
            try:
                return self.card_detection_feed()
            except Exception as e:
                print(f"❌ Error with card detection for camera 0: {e}")
                # Fallback về generate_frames thông thường
                return Response(self.generate_frames(camera_id),
                               mimetype='multipart/x-mixed-replace; boundary=frame')

        # Camera khác vẫn dùng generate_frames
        return Response(self.generate_frames(camera_id),
                        mimetype='multipart/x-mixed-replace; boundary=frame')

    def face_detection_feed(self):
        """Face detection feed using run_face_capture function"""
        try:
            import sys
            import os

            # Import face detection module
            # Get project root directory (parent of controllers)
            project_root = os.path.dirname(os.path.dirname(__file__))
            face_dir = os.path.join(project_root, 'face')
            sys.path.insert(0, face_dir)
            from face_detect import run_face_capture

            print("🎯 Starting face detection feed using run_face_capture...")

            # Prepare absolute model path
            model_path = os.path.join(face_dir, "scrfd_10g_bnkps.onnx")
            print(f"🎯 Using model path: {model_path}")
            print(f"🎯 Model exists: {os.path.exists(model_path)}")

            # Create frame source function that gets frames from camera 1
            def get_frame_from_camera():
                with self.lock_1:
                    if self.output_frame_1 is not None:
                        return self.output_frame_1.copy()
                    return None

            # Call run_face_capture with external frame source and camera controller
            return Response(run_face_capture(model_path=model_path, frame_source=get_frame_from_camera, camera_controller=self),
                           mimetype='multipart/x-mixed-replace; boundary=frame')

        except Exception as e:
            print(f"❌ Face detection feed error: {e}")
            # Return error response
            import cv2
            import numpy as np

            def error_generator():
                dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(dummy_frame, "Face Detection Error", (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                ret, buffer = cv2.imencode('.jpg', dummy_frame)
                frame_bytes = buffer.tobytes()
                while True:
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

            return Response(error_generator(),
                           mimetype='multipart/x-mixed-replace; boundary=frame')

    def card_detection_feed(self):
        try:
            import sys
            import os

            project_root = os.path.dirname(os.path.dirname(__file__))
            card_dir = os.path.join(project_root, 'card')
            sys.path.insert(0, card_dir)
            from card import detect_card_stream


            def get_frame_from_camera():
                with self.lock_0:
                    if self.output_frame_0 is not None:
                        return self.output_frame_0.copy()
                    return None

            return Response(detect_card_stream(frame_source=get_frame_from_camera),
                           mimetype='multipart/x-mixed-replace; boundary=frame')

        except Exception as e:
            print(f"Card detection feed error: {e}")
            # Return error response
            import cv2
            import numpy as np

            def error_generator():
                dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(dummy_frame, "Card Detection Error", (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                ret, buffer = cv2.imencode('.jpg', dummy_frame)
                frame_bytes = buffer.tobytes()
                while True:
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                    time.sleep(0.1)

            return Response(error_generator(),
                           mimetype='multipart/x-mixed-replace; boundary=frame')

    def auto_capture(self):

        self.streaming_active_1 = False
        self.capture_mode_1 = True

        import time
        time.sleep(0.2)

        if not self.session_model.current_session:
            session_id = self.session_model.create_session()
            print(f"🆕 Created new session for auto capture: {session_id}")

        timestamp = path_manager.get_unix_timestamp()
        filename = None

        try:
            with self.lock_1:
                if self.output_frame_1 is not None:
                    filename = path_manager.get_captured_image_path('face', timestamp)
                    cv2.imwrite(filename, self.output_frame_1)
                    print(f"Auto captured face image: {filename}")
                else:
                    print("No frame available from face camera")
                    self.streaming_active_1 = True
                    self.capture_mode_1 = False
                    return jsonify({'status': 'error', 'message': 'No face frame available'}), 500

            if filename:
                session_image_path = self.session_model.save_captured_image(filename, 'face')

                print(f"Auto captured face image: {filename}")
                print(f"Session status after auto capture: card={self.session_model.current_session.get('card_captured')}, face={self.session_model.current_session.get('face_captured')}")

                # Check if both images are captured and trigger AI pipeline
                self.check_and_trigger_ai_pipeline()

                print("Resuming face detection stream after auto capture...")
                self.streaming_active_1 = True
                self.capture_mode_1 = False
                print(f"Face camera resumed - streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

                return jsonify({
                    'status': 'success',
                    'message': 'Face auto captured successfully',
                    'image_path': filename,
                    'session_path': session_image_path,
                    'session_id': self.session_model.current_session.get('session_id'),
                    'capture_type': 'face_only',
                    'stream_resumed': True,
                    'ai_pipeline_triggered': self.session_model.current_session.get('card_captured', False),
                    'start_monitoring': True
                })
            else:
                self.streaming_active_1 = True
                self.capture_mode_1 = False
                return jsonify({'status': 'error', 'message': 'Failed to auto capture face'}), 500

        except Exception as e:
            print(f"❌ Auto capture error: {str(e)}")
            self.streaming_active_1 = True
            self.capture_mode_1 = False
            return jsonify({'status': 'error', 'message': f'Auto capture failed: {str(e)}'}), 500

    def check_and_trigger_ai_pipeline(self):
        try:
            session = self.session_model.current_session
            if not session:
                print("❌ No active session for AI pipeline trigger")
                return False

            card_captured = session.get('card_captured', False)
            face_captured = session.get('face_captured', False)
            card_image = session.get('card_image')
            face_image = session.get('face_image')

            print(f"🔍 AI Pipeline Check: card={card_captured}, face={face_captured}")
            print(f"   Card image: {card_image}")
            print(f"   Face image: {face_image}")

            if card_captured and face_captured and card_image and face_image:
                if os.path.exists(card_image) and os.path.exists(face_image):
                    print("🚀 Both images captured! Starting AI pipeline...")

                    import threading
                    def run_ai_pipeline():
                        try:
                            from app import app
                            from controllers.processing_controller import processing_controller
                            import flask

                            # Create mock request for processing
                            with app.app_context():
                                mock_request = type('MockRequest', (), {
                                    'is_json': True,
                                    'get_json': lambda: {'prompt_template': 'prompt'}
                                })()

                                # Backup original request
                                original_request = flask.request
                                flask.request = mock_request

                                try:
                                    print("🚀 Starting AI pipeline processing with real captured images...")
                                    result = processing_controller.process_images()

                                    if hasattr(result, 'get_json'):
                                        result_data = result.get_json()
                                        if result_data.get('status') == 'success':
                                            print("✅ Auto AI pipeline completed successfully")
                                            print(f"   Generated images: {len(result_data.get('generated_images', []))}")
                                        else:
                                            print(f"❌ Auto AI pipeline failed: {result_data.get('message')}")
                                    elif hasattr(result, 'status_code'):
                                        print(f"❌ Auto AI pipeline HTTP error: {result.status_code}")
                                    else:
                                        print(f"❌ Auto AI pipeline unknown response: {type(result)}")

                                except Exception as pipeline_error:
                                    print(f"❌ Auto AI pipeline processing error: {pipeline_error}")
                                    import traceback
                                    traceback.print_exc()
                                finally:
                                    flask.request = original_request

                        except Exception as e:
                            print(f"❌ Auto AI pipeline error: {e}")
                            import traceback
                            traceback.print_exc()

                    ai_thread = threading.Thread(target=run_ai_pipeline)
                    ai_thread.daemon = True
                    ai_thread.start()
                    return True
                else:
                    print(f"❌ Image files not found: card={os.path.exists(card_image) if card_image else False}, face={os.path.exists(face_image) if face_image else False}")
                    return False
            else:
                print(f"❌ Not ready for AI pipeline: card_captured={card_captured}, face_captured={face_captured}")
                return False

        except Exception as e:
            print(f"❌ Error checking AI pipeline trigger: {e}")
            import traceback
            traceback.print_exc()
            return False



    def get_current_session_id(self):
        """API endpoint để lấy session_id hiện tại cho auto capture"""
        try:
            if self.session_model.current_session:
                session_id = self.session_model.current_session.get('session_id')
                return jsonify({
                    'status': 'success',
                    'session_id': session_id
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'No active session'
                }), 404
        except Exception as e:
            print(f"❌ Error getting current session ID: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Failed to get session ID: {str(e)}'
            }), 500

    def generate_placeholder_frames(self, camera_id):
        """Generate placeholder frames for WebRTC-only mode"""
        import numpy as np
        import cv2

        # Create placeholder image
        frame = np.zeros((480, 640, 3), dtype=np.uint8)

        # Add text overlay
        camera_name = "Business Card Camera" if camera_id == 0 else "Face Camera"
        text_lines = [
            "WebRTC Mode Active",
            f"{camera_name}",
            "Using Client Camera",
            "via Browser"
        ]

        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        color = (255, 255, 255)
        thickness = 2

        y_offset = 150
        for i, line in enumerate(text_lines):
            text_size = cv2.getTextSize(line, font, font_scale, thickness)[0]
            x = (640 - text_size[0]) // 2
            y = y_offset + i * 40
            cv2.putText(frame, line, (x, y), font, font_scale, color, thickness)

        # Add camera icon (simple rectangle)
        cv2.rectangle(frame, (270, 80), (370, 120), (100, 100, 100), 2)
        cv2.circle(frame, (320, 100), 15, (100, 100, 100), 2)

        while True:
            # Encode frame
            ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
            if ret:
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

            time.sleep(0.1)  # 10 FPS for placeholder

    def pause_stream(self, camera_id):
        """Pause camera stream for capture processing"""
        try:
            if camera_id == 0:
                self.streaming_active_0 = False
                self.capture_mode_0 = True
            else:
                self.streaming_active_1 = False
                self.capture_mode_1 = True

            print(f"📸 Camera {camera_id} stream paused for capture processing")
            return jsonify({
                'status': 'success',
                'message': f'Camera {camera_id} stream paused',
                'camera_id': camera_id
            })
        except Exception as e:
            print(f"❌ Error pausing camera {camera_id}: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def resume_stream(self, camera_id):
        """Resume camera stream after capture processing"""
        try:
            if camera_id == 0:
                self.streaming_active_0 = True
                self.capture_mode_0 = False
            else:
                self.streaming_active_1 = True
                self.capture_mode_1 = False

            print(f"▶️ Camera {camera_id} stream resumed")
            return jsonify({
                'status': 'success',
                'message': f'Camera {camera_id} stream resumed',
                'camera_id': camera_id
            })
        except Exception as e:
            print(f"❌ Error resuming camera {camera_id}: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def stop_all_streams(self):
        """Stop all camera streams"""
        try:
            self.streaming_active_0 = False
            self.streaming_active_1 = False
            self.capture_mode_0 = True
            self.capture_mode_1 = True

            print("⏹️ All camera streams stopped")
            return jsonify({
                'status': 'success',
                'message': 'All camera streams stopped'
            })
        except Exception as e:
            print(f"❌ Error stopping streams: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def test_camera_control(self, camera_id, action):
        """Test camera control for debugging"""
        try:
            print(f"🧪 TEST: Camera {camera_id} action: {action}")

            if action == 'stop':
                if camera_id == 0:
                    self.streaming_active_0 = False
                    self.capture_mode_0 = True
                    print(f"🛑 TEST: Camera 0 STOPPED - streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
                else:
                    self.streaming_active_1 = False
                    self.capture_mode_1 = True
                    print(f"🛑 TEST: Camera 1 STOPPED - streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

                return jsonify({
                    'status': 'success',
                    'message': f'Camera {camera_id} stopped for testing',
                    'camera_id': camera_id,
                    'action': action
                })

            elif action == 'start':
                if camera_id == 0:
                    self.streaming_active_0 = True
                    self.capture_mode_0 = False
                    print(f"▶️ TEST: Camera 0 STARTED - streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
                else:
                    self.streaming_active_1 = True
                    self.capture_mode_1 = False
                    print(f"▶️ TEST: Camera 1 STARTED - streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

                return jsonify({
                    'status': 'success',
                    'message': f'Camera {camera_id} started for testing',
                    'camera_id': camera_id,
                    'action': action
                })
            else:
                return jsonify({'status': 'error', 'message': 'Invalid action. Use stop or start'}), 400

        except Exception as e:
            print(f"❌ Test camera control error: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def test_simple(self):
        """Simple test route"""
        print("🧪🧪🧪 SIMPLE TEST ROUTE CALLED 🧪🧪🧪")
        return jsonify({'status': 'success', 'message': 'Simple test works!'})

    def capture_step(self, camera_id):
        """Capture image from camera with IMMEDIATE stream stop"""
        print(f"🚨🚨🚨 CAPTURE_STEP CALLED FOR CAMERA {camera_id} 🚨🚨🚨")
        print(f"📸 Starting capture for camera {camera_id}")

        # STEP 1: IMMEDIATELY stop the camera stream
        print(f"🛑 STOPPING camera {camera_id} stream NOW...")
        if camera_id == 0:
            self.streaming_active_0 = False
            self.capture_mode_0 = True
            print(f"🛑 Camera 0: streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
        else:
            self.streaming_active_1 = False
            self.capture_mode_1 = True
            print(f"🛑 Camera 1: streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

        # STEP 2: Wait a moment to ensure stream stops
        import time
        time.sleep(0.2)  # Give time for stream to stop

        print(f"✅ Camera {camera_id} stream STOPPED for capture")

        # Ensure session exists
        if not self.session_model.current_session:
            session_id = self.session_model.create_session()
            print(f"🆕 Created new session: {session_id}")

        timestamp = path_manager.get_unix_timestamp()
        filename = None

        try:
            if camera_id == 0:
                with self.lock_0:
                    if self.output_frame_0 is not None:
                        filename = path_manager.get_captured_image_path('card', timestamp)
                        cv2.imwrite(filename, self.output_frame_0)
            elif camera_id == 1:
                with self.lock_1:
                    if self.output_frame_1 is not None:
                        filename = path_manager.get_captured_image_path('face', timestamp)
                        cv2.imwrite(filename, self.output_frame_1)
                        
            if filename:
                # Determine image type for session
                image_type = 'card' if camera_id == 0 else 'face'

                # Save to session and database
                session_image_path = self.session_model.save_captured_image(filename, image_type)

                print(f"✅ Captured {image_type} image: {filename}")
                print(f"📁 Session status: card={self.session_model.current_session.get('card_captured')}, face={self.session_model.current_session.get('face_captured')}")

                # Log database checkin status
                checkin_id = self.session_model.current_session.get('db_checkin_id')
                if checkin_id:
                    print(f"💾 Database checkin record ID: {checkin_id}")
                else:
                    print("⚠️ No database checkin record created yet")

                # STEP 3: Resume camera stream after successful capture
                print(f"▶️ Resuming camera {camera_id} stream after capture...")
                if camera_id == 0:
                    self.streaming_active_0 = True
                    self.capture_mode_0 = False
                    print(f"▶️ Camera 0 resumed - streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
                else:
                    self.streaming_active_1 = True
                    self.capture_mode_1 = False
                    print(f"▶️ Camera 1 resumed - streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

                # STEP 4: Check and trigger AI pipeline if both images are captured
                print("🔍 Checking if AI pipeline should be triggered...")
                self.check_and_trigger_ai_pipeline()

                import os
                relative_path = os.path.relpath(filename, os.getcwd())
                
                print(f"🔍 DEBUG: Original filename: {filename}")
                print(f"🔍 DEBUG: Relative path: {relative_path}")
                print(f"🔍 DEBUG: Current working directory: {os.getcwd()}")
                
                return jsonify({
                    'status': 'success',
                    'message': f'{image_type.title()} captured successfully',
                    'image_path': relative_path,
                    'session_path': session_image_path,
                    'session_id': self.session_model.current_session.get('session_id'),
                    'stream_resumed': True
                })
            else:
                return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.'}), 500
                
        except Exception as e:
            print(f"Error capturing image: {str(e)}")
            return jsonify({'status': 'error', 'message': f'Lỗi khi chụp ảnh: {str(e)}'}), 500
        else:
            # Resume stream even on error
            print(f"❌ Capture failed, resuming camera {camera_id} stream...")
            if camera_id == 0:
                self.streaming_active_0 = True
                self.capture_mode_0 = False
            else:
                self.streaming_active_1 = True
                self.capture_mode_1 = False

            return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.', 'stream_resumed': True}), 500

    def upload_camera_image(self):
        """Handle image upload from client-side camera (WebRTC)"""

        try:
            if 'image' not in request.files:
                return jsonify({'status': 'error', 'message': 'No image file provided'}), 400

            file = request.files['image']
            camera_id = int(request.form.get('camera_id', 0))

            if file.filename == '':
                return jsonify({'status': 'error', 'message': 'No file selected'}), 400

            # Ensure session exists
            if not self.session_model.current_session:
                session_id = self.session_model.create_session()
                print(f"🆕 Created new session for upload: {session_id}")

            # Save uploaded file using PathManager
            timestamp = path_manager.get_unix_timestamp()
            image_type = 'card' if camera_id == 0 else 'face'
            filename = path_manager.get_captured_image_path(image_type, timestamp)

            file.save(filename)

            # Save to session
            session_image_path = self.session_model.save_captured_image(filename, image_type)

            print(f"✅ Client uploaded {image_type} image: {filename}")
            print(f"📁 Session status: card={self.session_model.current_session.get('card_captured')}, face={self.session_model.current_session.get('face_captured')}")

            return jsonify({
                'status': 'success',
                'message': f'{image_type.title()} uploaded successfully',
                'image_path': filename,
                'session_path': session_image_path,
                'session_id': self.session_model.current_session.get('session_id')
            })

        except Exception as e:
            print(f"❌ Error uploading image: {str(e)}")
            return jsonify({'status': 'error', 'message': f'Upload failed: {str(e)}'}), 500

    def adjust_focus(self):
        """Adjust focus for camera 0 (business card camera) - EXACT project_directory style"""
        data = request.get_json()
        new_focus_value = data.get('focus_value')

        if new_focus_value is None:
            return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

        try:
            new_focus_value = int(new_focus_value)
        except ValueError:
            return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

        with self.lock_0:
            if self.camera_0 and self.camera_0.isOpened():
                self.camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
                self.initial_focus_value = new_focus_value
                print(f"Focus mới cho Logitech: {new_focus_value}")
                return jsonify({'status': 'success', 'message': f'Đã chỉnh nét thành {new_focus_value}.'})
            else:
                return jsonify({'status': 'error', 'message': 'Camera Logitech không hoạt động.'}), 500

    def session_status(self):
        """Get current session status"""
        try:
            status = self.session_model.get_session_status()
            return jsonify({
                'status': 'success',
                **status
            })
        except Exception as e:
            print(f"❌ Session status error: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def camera_status_api(self):
        """API endpoint to get camera status with WebRTC mode support"""
        try:
            # Build camera status
            cameras = {}

            if self.webrtc_only_mode:
                # WebRTC-only mode - report cameras as available for client-side use
                cameras['0'] = {
                    'opened': True,
                    'streaming': True,
                    'resolution': "640x480",
                    'focus': self.initial_focus_value,
                    'mode': 'webrtc',
                    'source': 'client'
                }
                cameras['1'] = {
                    'opened': True,
                    'streaming': True,
                    'resolution': "640x480",
                    'focus': 'auto',
                    'mode': 'webrtc',
                    'source': 'client'
                }
                camera0_available = True
                camera1_available = True
            else:
                # Physical camera mode
                camera0_available = self.camera_0 and self.camera_0.isOpened()
                if camera0_available:
                    cameras['0'] = {
                        'opened': True,
                        'streaming': True,
                        'resolution': "640x480",
                        'focus': self.initial_focus_value,
                        'mode': 'physical',
                        'source': 'server'
                    }
                else:
                    cameras['0'] = {
                        'opened': False,
                        'streaming': False,
                        'resolution': 'N/A',
                        'focus': self.initial_focus_value,
                        'mode': 'physical',
                        'source': 'server'
                    }

                # Camera 1 status
                camera1_available = self.camera_1 and self.camera_1.isOpened()
                if camera1_available:
                    cameras['1'] = {
                        'opened': True,
                        'streaming': True,
                        'resolution': "640x480",
                        'focus': 'auto',
                        'mode': 'physical',
                        'source': 'server'
                    }
                else:
                    cameras['1'] = {
                        'opened': False,
                        'streaming': False,
                        'resolution': 'N/A',
                        'focus': 'auto',
                        'mode': 'physical',
                        'source': 'server'
                    }

            result = {
                'status': 'success',
                'cameras': cameras,
                'camera_count': 2 if (camera0_available and camera1_available) else (1 if (camera0_available or camera1_available) else 0),
                'camera0_available': camera0_available,  # Add explicit fields for JS
                'camera1_available': camera1_available,  # Add explicit fields for JS
                'webrtc_only_mode': self.webrtc_only_mode,  # Important for client-side logic
                'server_environment': self.is_server_environment,
                'config': {
                    'width': 640,
                    'height': 480,
                    'fps': 30,
                    'backend': 'WebRTC' if self.webrtc_only_mode else 'DirectShow'
                }
            }

            print(f"📊 Camera status API response: camera0={camera0_available}, camera1={camera1_available}")
            return jsonify(result)

        except Exception as e:
            print(f"❌ Camera status error: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e),
                'camera0_available': False,
                'camera1_available': False
            }), 500
    
    def get_camera_status(self):
        """Get current camera status"""
        try:
            # Check if cameras are initialized and working
            camera0_status = (self.camera_0 is not None and 
                             self.camera_0.isOpened() if hasattr(self, 'camera_0') else False)
            camera1_status = (self.camera_1 is not None and 
                             self.camera_1.isOpened() if hasattr(self, 'camera_1') else False)
            
            print(f"📊 Camera status check:")
            print(f"   Camera 0 (Business Card): {camera0_status}")
            print(f"   Camera 1 (Face): {camera1_status}")
            
            return jsonify({
                'camera0_available': camera0_status,
                'camera1_available': camera1_status,
                'camera0': camera0_status,  # Backward compatibility
                'camera1': camera1_status,  # Backward compatibility
                'status': 'success'
            })
            
        except Exception as e:
            print(f"❌ Error checking camera status: {e}")
            return jsonify({
                'camera0_available': False,
                'camera1_available': False,
                'camera0': False,
                'camera1': False,
                'status': 'error',
                'error': str(e)
            }), 500

    def cleanup(self):
        """Public cleanup method"""
        self.cleanup_cameras()

    def reset_face_camera(self):
        """Reset face camera state to resume streaming"""
        try:
            print("🔄 Resetting face camera state...")
            
            # Reset capture mode
            self.capture_mode_1 = False
            
            # Resume streaming
            self.streaming_active_1 = True
            
            print("✅ Face camera reset completed - streaming resumed")
            return True
        except Exception as e:
            print(f"❌ Error resetting face camera: {e}")
            return False

    def reset_card_camera(self):
        """Reset card camera state to resume streaming"""
        try:
            print("🔄 Resetting card camera state...")
            
            self.capture_mode_0 = False
            
            self.streaming_active_0 = True
            
            print("✅ Card camera reset completed - streaming resumed")
            return True
        except Exception as e:
            print(f"❌ Error resetting card camera: {e}")
            return False

    def reset_face_camera_api(self):
        """API endpoint to reset face camera"""
        try:
            success = self.reset_face_camera()
            
            if success:
                return jsonify({
                    'status': 'success',
                    'message': 'Face camera reset successfully - streaming resumed'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to reset face camera'
                }), 500
                
        except Exception as e:
            print(f"❌ Error in reset_face_camera_api: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Error resetting face camera: {str(e)}'
            }), 500

    def reset_card_camera_api(self):
        """API endpoint to reset card camera"""
        try:
            success = self.reset_card_camera()
            
            if success:
                return jsonify({
                    'status': 'success',
                    'message': 'Card camera reset successfully - streaming resumed'
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to reset card camera'
                }), 500
                
        except Exception as e:
            print(f"❌ Error in reset_card_camera_api: {e}")
            return jsonify({
                'status': 'error',
                'message': f'Error resetting card camera: {str(e)}'
            }), 500

    def cleanup_cameras(self):
        """Cleanup camera resources (EXACT project_directory style)"""
        print("Cleaning up cameras...")

        # Release camera 0
        try:
            with self.lock_0:
                if self.camera_0:
                    self.camera_0.release()
                    print("Released camera 0")
                self.camera_0 = None
                self.output_frame_0 = None
        except Exception as e:
            print(f"Error releasing camera 0: {e}")

        # Release camera 1
        try:
            with self.lock_1:
                if self.camera_1:
                    self.camera_1.release()
                    print("Released camera 1")
                self.camera_1 = None
                self.output_frame_1 = None
        except Exception as e:
            print(f"Error releasing camera 1: {e}")

        # Clear OpenCV windows
        try:
            cv2.destroyAllWindows()
        except Exception as e:
            print(f"Error destroying OpenCV windows: {e}")

        print("Camera cleanup completed")

# Create global camera controller instance
camera_controller = CameraController()

def get_camera_blueprint():
    """Get camera controller blueprint"""
    return camera_controller.blueprint




