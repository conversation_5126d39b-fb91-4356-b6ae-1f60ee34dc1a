class FaceCameraMonitor {
    constructor() {
        this.isFaceCameraFrozen = false;
        this.fastPollInterval = null;
        this.slowPollInterval = null;

        this.config = {
            fastPollInterval: 1000,
            slowPollInterval: 10000,
            fastPollTimeout: 60000,
            apiEndpoint: '/api/session_status'
        };

        this.elements = {
            faceImg: null,
            statusDot: null,
            statusText: null
        };
    }

    init() {
        this.cacheElements();
        this.startSlowPolling();
        this.startFastPolling();
    }

    cacheElements() {
        this.elements.faceImg = document.getElementById('cam1');
        this.elements.statusDot = document.getElementById('status1');
        this.elements.statusText = document.getElementById('faceStatus');
    }

    async checkAndFreezeFaceCamera() {
        try {
            const response = await fetch(this.config.apiEndpoint);
            const data = await response.json();

            if (data.status === 'success' && data.session) {
                const session = data.session;

                if (session.face_captured && !this.isFaceCameraFrozen) {
                    const faceImagePath = session.face_image;
                    if (faceImagePath) {
                        this.freezeFaceCamera(faceImagePath);
                    } else {
                        this.freezeFaceCameraGeneric();
                    }

                    if (typeof startAutoProcessingMonitor === 'function') {
                        startAutoProcessingMonitor();
                    }
                }
            }
        } catch (error) {
            // Silent error handling
        }
    }

    freezeFaceCamera(imagePath) {
        if (this.elements.faceImg) {
            this.elements.faceImg.src = '/' + imagePath + '?t=' + new Date().getTime();
            this.elements.faceImg.style.border = '3px solid #43e97b';
            this.isFaceCameraFrozen = true;
        }

        this.updateVisualFeedback();
        this.stopFastPolling();
    }

    freezeFaceCameraGeneric() {
        if (this.elements.faceImg) {
            this.elements.faceImg.style.border = '3px solid #43e97b';
            this.isFaceCameraFrozen = true;
        }

        this.updateVisualFeedback();
        this.stopFastPolling();
    }

    updateVisualFeedback() {
        if (this.elements.statusDot) {
            this.elements.statusDot.style.background = '#43e97b';
            this.elements.statusDot.className = 'status-dot status-captured';
        }

        if (this.elements.statusText) {
            this.elements.statusText.textContent = '✅ Đã chụp xong!';
            this.elements.statusText.style.color = '#43e97b';
        }
    }

    startFastPolling() {
        if (this.fastPollInterval) {
            clearInterval(this.fastPollInterval);
        }

        this.fastPollInterval = setInterval(() => {
            if (!this.isFaceCameraFrozen) {
                this.checkAndFreezeFaceCamera();
            } else {
                this.stopFastPolling();
            }
        }, this.config.fastPollInterval);

        setTimeout(() => {
            this.stopFastPolling();
        }, this.config.fastPollTimeout);
    }

    stopFastPolling() {
        if (this.fastPollInterval) {
            clearInterval(this.fastPollInterval);
            this.fastPollInterval = null;
        }
    }

    startSlowPolling() {
        if (this.slowPollInterval) {
            clearInterval(this.slowPollInterval);
        }

        this.slowPollInterval = setInterval(() => {
            if (!this.isFaceCameraFrozen) {
                this.checkAndFreezeFaceCamera();
            }
        }, this.config.slowPollInterval);
    }

    stopSlowPolling() {
        if (this.slowPollInterval) {
            clearInterval(this.slowPollInterval);
            this.slowPollInterval = null;
        }
    }

    async reset() {
        try {
            const response = await fetch('/reset_face_camera', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                console.log('✅ Face camera reset successfully');
                
                this.isFaceCameraFrozen = false;
                
                if (this.elements.faceImg) {
                    const timestamp = new Date().getTime();
                    const url = new URL('/face_detection_feed', window.location.origin);
                    url.searchParams.set('t', timestamp);
                    this.elements.faceImg.src = url.toString();
                    this.elements.faceImg.style.border = '';
                }
                
                if (this.elements.statusDot) {
                    this.elements.statusDot.style.background = '';
                    this.elements.statusDot.className = 'status-dot status-ready';
                }
                
                if (this.elements.statusText) {
                    this.elements.statusText.textContent = '😊 Sẵn sàng';
                    this.elements.statusText.style.color = '';
                }
                
                this.stopFastPolling();
                this.startFastPolling();
                
            } else {
                console.error('❌ Failed to reset face camera:', data.message);
            }
        } catch (error) {
            console.error('❌ Error resetting face camera:', error);
        }
    }

    destroy() {
        this.stopFastPolling();
        this.stopSlowPolling();
    }
}

window.faceCameraMonitor = null;

document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (!window.faceCameraMonitor) {
            window.faceCameraMonitor = new FaceCameraMonitor();
            window.faceCameraMonitor.init();
        }
    }, 1000);
});

if (typeof module !== 'undefined' && module.exports) {
    module.exports = FaceCameraMonitor;
}
