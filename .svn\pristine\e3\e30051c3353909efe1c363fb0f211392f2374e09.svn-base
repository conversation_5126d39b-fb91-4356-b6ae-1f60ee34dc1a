<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tìm kiếm thông tin khách hàng - AI Generator</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search_database.css') }}">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="apple-nav">
        <div class="apple-nav-content">
            <a href="/" class="apple-logo">
                <img src="{{ url_for('static', filename='images/ssg-logo.png') }}" alt="SSG Logo" class="nav-logo-img">
                <span class="nav-title">AI Generator - Tìm kiếm thông tin khách hàng</span>
            </a>
            <div class="nav-actions">
                <a href="/" class="nav-btn">
                    <i class="fas fa-home"></i>
                    Trang chủ
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="search-container">
        <!-- Header Section -->
        <div class="search-header">
            <h1><i class="fas fa-search"></i> Tìm kiếm thông tin khách hàng</h1>
        </div>

        <!-- Search Controls -->
        <div class="search-controls">
            <div class="search-form">
                <div class="search-row">
                    <div class="search-field">
                        <label for="companyInput">🏢 Công ty:</label>
                        <input type="text" id="companyInput" placeholder="Nhập tên công ty..." class="search-input">
                    </div>
                    <div class="search-field">
                        <label for="nameInput">👤 Họ tên:</label>
                        <input type="text" id="nameInput" placeholder="Nhập họ tên..." class="search-input">
                    </div>
                    <div class="search-field">
                        <label for="phoneInput">📞 Số điện thoại:</label>
                        <input type="text" id="phoneInput" placeholder="Nhập số điện thoại..." class="search-input">
                    </div>
                </div>

                <div class="search-row">
                    <div class="search-field">
                        <label for="dateFromInput">📅 Thời gian check-in từ:</label>
                        <input type="datetime-local" id="dateFromInput" class="search-input date-input">
                    </div>
                    <div class="search-field">
                        <label for="dateToInput">📅 Đến:</label>
                        <input type="datetime-local" id="dateToInput" class="search-input date-input">
                    </div>
                    <div class="search-actions">
                        <button id="searchBtn" class="search-btn">
                            <i class="fas fa-search"></i>
                            Tìm kiếm
                        </button>
                        <button id="clearBtn" class="clear-btn">
                            <i class="fas fa-times"></i>
                            Xóa bộ lọc
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Container -->
        <div class="tables-container">
            <!-- Customer Info Table Section -->
            <div class="table-section">
                <div class="table-header">
                    <h2><i class="fas fa-users"></i> Thông tin khách hàng</h2>
                    <div class="table-controls">
                        <button id="sortToggleBtn" class="sort-btn" title="Đảo ngược thứ tự">
                            <i class="fas fa-sort-amount-down"></i>
                            <span id="sortText">Xem gần nhất</span>
                        </button>
                        <div class="table-info">
                            <span id="customerInfoCount">Đang tải...</span>
                        </div>
                    </div>
                </div>

                <div class="table-wrapper">
                    <div id="customerInfoLoading" class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Đang tải dữ liệu...
                    </div>
                    <table id="customerInfoTable" class="data-table" style="display: none;">
                        <thead>
                            <tr>
                                <th>STT</th>
                                <th>Họ tên</th>
                                <th>Công ty</th>
                                <th>Chức vụ</th>
                                <th>Email</th>
                                <th>Điện thoại</th>
                                <th>Địa chỉ</th>
                                <th>Website</th>
                                <th>Thời gian check-in</th>
                                <th>Ảnh Card</th>
                                <th>Ảnh Face</th>
                                <th>Ảnh AI</th>
                            </tr>
                        </thead>
                        <tbody id="customerInfoTableBody">
                            <!-- Data will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div id="customerInfoPagination" class="pagination">
                        <!-- Pagination will be generated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div id="errorModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Lỗi</h3>
                <span class="close" onclick="closeErrorModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer">
                <button onclick="closeErrorModal()" class="btn btn-secondary">Đóng</button>
            </div>
        </div>
    </div>

    <!-- Image Preview Modal -->
    <div id="imageModal" class="modal" style="display: none;">
        <div class="modal-content image-modal-content">
            <div class="modal-header">
                <h3 id="imageModalTitle">Xem ảnh</h3>
                <span class="close" onclick="closeImageModal()">&times;</span>
            </div>
            <div class="modal-body">
                <img id="imagePreview" src="" alt="Image Preview" class="image-preview">
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/search_database.js') }}"></script>
</body>
</html>
