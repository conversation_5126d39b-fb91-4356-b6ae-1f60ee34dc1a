
from database.connection import db

def upgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('''
            CREATE TABLE IF NOT EXISTS TBL_CHECKIN (
                checkin_id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                history_id INTEGER,
                checkin_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                card_img TEXT,
                face_img TEXT,
                ai_img TEXT,
                FOREIGN KEY (customer_id) REFERENCES TBL_CUSTOMER (customer_id),
                FOREIGN KEY (history_id) REFERENCES TBL_CUSTOMER_HIS (history_id)
            )
        '''))
        conn.commit()


def downgrade():
    with db.engine.connect() as conn:
        conn.execute(db.text('DROP TABLE IF EXISTS TBL_CHECKIN'))
        conn.commit()
