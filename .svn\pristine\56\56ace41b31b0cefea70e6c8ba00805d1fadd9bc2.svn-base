/* ========================================
   PROGRESSIVE DISCLOSURE CSS - Animations and transitions
   Section visibility states and smooth animations
   Includes Apple Design System animations and loading states
   ======================================== */

/* ===== APPLE ANIMATIONS ===== */
.apple-fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.apple-scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

/* ===== APPLE LOADING STATES ===== */
.apple-loading {
  position: relative;
  overflow: hidden;
}

.apple-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== APPLE RIPPLE EFFECT ===== */
.apple-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== APPLE SPINNER ===== */
.apple-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.apple-spinner-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-primary);
  animation: spinner-bounce 1.4s ease-in-out infinite both;
}

.apple-spinner-dot:nth-child(1) { animation-delay: -0.32s; }
.apple-spinner-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes spinner-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* PROGRESSIVE DISCLOSURE & ANIMATIONS */

/* Hidden section - Section ẩn ban đầu */
.section-hidden {
    opacity: 0;                                  /* Trong suốt hoàn toàn */
    transform: translateY(30px);                 /* Dịch chuyển xuống */
    transition: all 0.6s ease-in-out;          /* Hiệu ứng chuyển đổi */
    pointer-events: none;                        /* Không thể tương tác */
    max-height: 0;                              /* Chiều cao 0 */
    overflow: hidden;                           /* Ẩn nội dung tràn */
}

/* FIX DROPDOWN STYLE SELECTION */
#promptSelect {
    position: relative !important;
    z-index: 1000 !important;
    pointer-events: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.control-group {
    position: relative;
    z-index: 100;
}

/* Visible section - Section hiển thị */
.section-visible {
    opacity: 1;                                  /* Hiển thị hoàn toàn */
    transform: translateY(0);                    /* Vị trí bình thường */
    transition: all 0.6s ease-in-out;          /* Hiệu ứng chuyển đổi */
    pointer-events: auto;                        /* Có thể tương tác */
    max-height: none;                           /* Chiều cao tự động */
    overflow: visible;                          /* Hiển thị nội dung */
}

/* Slide in animation - Hiệu ứng trượt vào */
.slide-in {
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fade in animation - Hiệu ứng mờ dần */
.fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ========================================
   EMPTY STATE STYLES
   ======================================== */

/* Empty state - Trạng thái rỗng khi không có dữ liệu */
.empty-state {
    text-align: center;                                  /* Căn giữa text */
    padding: var(--spacing-3xl);                        /* Padding rất lớn */
    color: var(--color-text-secondary);                 /* Màu chữ phụ */
}

/* Empty state icon - Icon trạng thái rỗng */
.empty-state-icon {
    font-size: 48px;                                    /* Font rất lớn */
    margin-bottom: var(--spacing-md);                   /* Khoảng cách dưới vừa */
    opacity: 0.5;                                       /* Độ trong suốt 50% */
}

/* ========================================
   LOADING STATES & TRANSITIONS
   ======================================== */

/* Loading spinner animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Smooth transitions for interactive elements */
.smooth-transition {
    transition: all 0.3s ease;
}

/* Hover effects */
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Focus states */
.focus-visible:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* ========================================
   PROGRESS INDICATORS
   ======================================== */

/* Progress bar container */
.progress-container {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: var(--spacing-md) 0;
}

/* Progress bar fill */
.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary), var(--color-success));
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

/* Progress bar animation */
.progress-animated {
    background: linear-gradient(90deg, 
        var(--color-primary) 0%, 
        var(--color-success) 50%, 
        var(--color-primary) 100%);
    background-size: 200% 100%;
    animation: progressShimmer 2s linear infinite;
}

@keyframes progressShimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* ========================================
   TOAST NOTIFICATIONS
   ======================================== */

/* Toast container */
#toastContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
}

/* Toast notification */
.toast {
    background: var(--color-primary);
    color: white;
    padding: 12px 20px;
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    font-size: 14px;
    font-weight: 500;
    max-width: 300px;
    word-wrap: break-word;
    pointer-events: auto;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

/* Toast variants */
.toast.success { background: var(--color-success); }
.toast.error { background: var(--color-error); }
.toast.warning { background: var(--color-warning); }
.toast.info { background: var(--color-primary); }

/* Toast show state */
.toast.show {
    transform: translateX(0);
}

/* ========================================
   MICRO-INTERACTIONS
   ======================================== */

/* Button press effect */
.press-effect:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* Ripple effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Shake animation for errors */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
