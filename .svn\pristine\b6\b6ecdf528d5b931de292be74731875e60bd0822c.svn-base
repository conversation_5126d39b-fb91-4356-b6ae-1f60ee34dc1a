import os
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

db = SQLAlchemy()
migrate = Migrate()

def init_database(app):
    db_path = os.path.abspath('database/data/ai_gen.db')
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_timeout': 30,
        'pool_recycle': 300,
        'pool_pre_ping': True,
        'connect_args': {
            'timeout': 60,  # Tăng timeout
            'check_same_thread': False,
            'isolation_level': None  # Auto-commit mode
        }
    }

    data_dir = 'database/data'
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    backup_dir = 'database/data/backups'
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)

    db.init_app(app)
    migrate.init_app(app, db, directory='database/migrations')

    with app.app_context():
        # Ensure models are imported so SQLAlchemy knows the tables before create_all
        try:
            from database.models import Customer, Checkin, CustomerHistory  # noqa: F401
        except Exception as e:
            # If models cannot be imported, tables won't be created
            print(f"⚠️ Warning: failed to import models before create_all: {e}")

        db.create_all()

        try:
            with db.engine.connect() as conn:
                conn.execute(db.text('PRAGMA journal_mode=WAL'))
                conn.execute(db.text('PRAGMA synchronous=NORMAL'))
                conn.execute(db.text('PRAGMA cache_size=1000'))
                conn.execute(db.text('PRAGMA temp_store=memory'))
                conn.commit()
        except Exception:
            pass

    return db
