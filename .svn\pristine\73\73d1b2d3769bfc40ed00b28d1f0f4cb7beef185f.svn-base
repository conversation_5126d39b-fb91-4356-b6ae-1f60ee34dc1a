/**
 * Search Database JavaScript - X<PERSON> lý tìm kiếm và hiển thị dữ liệu database
 */

class SearchDatabase {
    constructor() {
        this.currentCustomerInfoPage = 1;
        this.perPage = 20;
        this.currentSearchParams = {};
        this.currentSortOrder = 'desc'; // 'desc' = gần nhất, 'asc' = lâu nhất

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInitialData();
    }
    
    bindEvents() {
        // Search button click
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.performSearch();
        });

        // Clear button click
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearSearch();
        });

        // Sort toggle button click
        document.getElementById('sortToggleBtn').addEventListener('click', () => {
            this.toggleSort();
        });

        // Enter key in search inputs
        const searchInputs = ['companyInput', 'nameInput', 'phoneInput', 'dateFromInput', 'dateToInput'];
        searchInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch();
                    }
                });
            }
        });

        // Modal close events
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModals();
            }
        });
    }
    
    loadInitialData() {
        this.loadCustomerInfo(1, {});
    }

    performSearch() {
        const searchParams = this.getSearchParams();
        this.currentSearchParams = searchParams;
        this.currentCustomerInfoPage = 1;

        this.loadCustomerInfo(1, searchParams);
    }

    getSearchParams() {
        return {
            company: document.getElementById('companyInput').value.trim(),
            name: document.getElementById('nameInput').value.trim(),
            phone: document.getElementById('phoneInput').value.trim(),
            dateFrom: document.getElementById('dateFromInput').value,
            dateTo: document.getElementById('dateToInput').value
        };
    }

    clearSearch() {
        document.getElementById('companyInput').value = '';
        document.getElementById('nameInput').value = '';
        document.getElementById('phoneInput').value = '';
        document.getElementById('dateFromInput').value = '';
        document.getElementById('dateToInput').value = '';

        this.currentSearchParams = {};
        this.currentCustomerInfoPage = 1;

        this.loadCustomerInfo(1, {});
    }

    toggleSort() {
        // Đảo ngược thứ tự sắp xếp
        this.currentSortOrder = this.currentSortOrder === 'desc' ? 'asc' : 'desc';

        // Cập nhật giao diện nút
        const sortBtn = document.getElementById('sortToggleBtn');
        const sortText = document.getElementById('sortText');
        const sortIcon = sortBtn.querySelector('i');

        if (this.currentSortOrder === 'desc') {
            sortIcon.className = 'fas fa-sort-amount-down';
            sortText.textContent = 'Xem gần nhất';
        } else {
            sortIcon.className = 'fas fa-sort-amount-up';
            sortText.textContent = 'Xem lâu nhất';
        }

        // Tải lại dữ liệu với thứ tự mới
        this.currentCustomerInfoPage = 1;
        this.loadCustomerInfo(1, this.currentSearchParams || {});
    }
    
    async loadCustomerInfo(page, searchParams) {
        try {
            this.showLoading('customerInfo');

            const params = new URLSearchParams({
                page: page,
                per_page: this.perPage,
                company: searchParams.company || '',
                name: searchParams.name || '',
                phone: searchParams.phone || '',
                date_from: searchParams.dateFrom || '',
                date_to: searchParams.dateTo || '',
                sort_order: this.currentSortOrder
            });

            const response = await fetch(`/search/customer_info?${params}`);
            const data = await response.json();

            if (data.success) {
                this.renderCustomerInfoTable(data.data);
                this.renderCustomerInfoPagination(data.pagination);
                this.updateCustomerInfoCount(data.pagination.total);
                this.currentCustomerInfoPage = page;
            } else {
                this.showError('Lỗi tải dữ liệu thông tin khách hàng: ' + data.error);
            }

        } catch (error) {
            console.error('Error loading customer info:', error);
            this.showError('Lỗi kết nối khi tải dữ liệu thông tin khách hàng');
        } finally {
            this.hideLoading('customerInfo');
        }
    }
    

    
    renderCustomerInfoTable(customerInfos) {
        const tbody = document.getElementById('customerInfoTableBody');
        tbody.innerHTML = '';

        if (customerInfos.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" style="text-align: center; padding: 20px;">Không có dữ liệu</td></tr>';
            return;
        }

        customerInfos.forEach(info => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${info.stt}</td>
                <td title="${info.customer_name}">${this.truncateText(info.customer_name, 20)}</td>
                <td title="${info.company_name}">${this.truncateText(info.company_name, 20)}</td>
                <td title="${info.customer_title}">${this.truncateText(info.customer_title, 15)}</td>
                <td title="${info.customer_email}">${this.truncateText(info.customer_email, 25)}</td>
                <td>${info.customer_tel}</td>
                <td title="${info.customer_addr}">${this.truncateText(info.customer_addr, 30)}</td>
                <td title="${info.customer_web}">${this.truncateText(info.customer_web, 20)}</td>
                <td>${info.checkin_time || '-'}</td>
                <td>${this.renderImageLink(info.card_img, 'Card')}</td>
                <td>${this.renderImageLink(info.face_img, 'Face')}</td>
                <td>${this.renderImageLink(info.ai_img, 'AI')}</td>
            `;
            tbody.appendChild(row);
        });

        document.getElementById('customerInfoTable').style.display = 'table';
    }
    

    
    renderImageLink(imagePath, type) {
        if (!imagePath) return '-';

        // Process image path to handle backslashes and ensure proper format
        let processedPath = imagePath;

        // Convert backslashes to forward slashes
        processedPath = processedPath.replace(/\\/g, '/');

        // Ensure proper path format
        const fullImagePath = processedPath.startsWith('/') ? processedPath : '/' + processedPath;

        console.log(`🖼️ Image link: '${imagePath}' → '${fullImagePath}'`);

        return `<a href="#" class="image-link" onclick="searchDB.showImage('${fullImagePath}', '${type}')">
                    <i class="fas fa-image"></i> ${type}
                </a>`;
    }
    
    renderCustomerInfoPagination(pagination) {
        this.renderPagination('customerInfoPagination', pagination, (page) => {
            this.loadCustomerInfo(page, this.currentSearchParams || {});
        });
    }
    
    renderPagination(containerId, pagination, onPageClick) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        if (pagination.total_pages <= 1) return;
        
        // Previous button
        const prevBtn = document.createElement('button');
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i> Trước';
        prevBtn.disabled = !pagination.has_prev;
        prevBtn.onclick = () => onPageClick(pagination.prev_num);
        container.appendChild(prevBtn);
        
        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.page + 2);
        
        if (startPage > 1) {
            const firstBtn = document.createElement('button');
            firstBtn.textContent = '1';
            firstBtn.onclick = () => onPageClick(1);
            container.appendChild(firstBtn);
            
            if (startPage > 2) {
                const dots = document.createElement('span');
                dots.textContent = '...';
                dots.style.padding = '8px';
                container.appendChild(dots);
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.className = i === pagination.page ? 'active' : '';
            pageBtn.onclick = () => onPageClick(i);
            container.appendChild(pageBtn);
        }
        
        if (endPage < pagination.total_pages) {
            if (endPage < pagination.total_pages - 1) {
                const dots = document.createElement('span');
                dots.textContent = '...';
                dots.style.padding = '8px';
                container.appendChild(dots);
            }
            
            const lastBtn = document.createElement('button');
            lastBtn.textContent = pagination.total_pages;
            lastBtn.onclick = () => onPageClick(pagination.total_pages);
            container.appendChild(lastBtn);
        }
        
        // Next button
        const nextBtn = document.createElement('button');
        nextBtn.innerHTML = 'Sau <i class="fas fa-chevron-right"></i>';
        nextBtn.disabled = !pagination.has_next;
        nextBtn.onclick = () => onPageClick(pagination.next_num);
        container.appendChild(nextBtn);
        
        // Page info
        const info = document.createElement('div');
        info.className = 'pagination-info';
        info.textContent = `Trang ${pagination.page} / ${pagination.total_pages} (${pagination.total} bản ghi)`;
        container.appendChild(info);
    }
    
    updateCustomerInfoCount(total) {
        document.getElementById('customerInfoCount').textContent = `${total} bản ghi`;
    }
    
    showLoading(type) {
        document.getElementById(`${type}Loading`).style.display = 'block';
        document.getElementById(`${type}Table`).style.display = 'none';
    }

    hideLoading(type) {
        document.getElementById(`${type}Loading`).style.display = 'none';
    }
    
    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    showImage(imagePath, type) {
        console.log('🖼️ showImage called:', { imagePath, type });

        if (!imagePath || imagePath === '-') {
            this.showError(`Không có ảnh ${type} để hiển thị`);
            return;
        }

        // Ensure proper path format
        let fullImagePath = imagePath;
        if (!fullImagePath.startsWith('http') && !fullImagePath.startsWith('/')) {
            fullImagePath = '/' + fullImagePath;
        }

        console.log('🖼️ Final image path:', fullImagePath);

        document.getElementById('imageModalTitle').textContent = `Xem ảnh ${type}`;

        const imagePreview = document.getElementById('imagePreview');
        imagePreview.src = fullImagePath;

        // Add error handling for image loading
        imagePreview.onerror = () => {
            console.error('❌ Failed to load image:', fullImagePath);
            this.showError(`Không thể tải ảnh ${type}. Đường dẫn: ${fullImagePath}`);
            this.closeImageModal();
        };

        imagePreview.onload = () => {
            console.log('✅ Image loaded successfully:', fullImagePath);
        };

        document.getElementById('imageModal').style.display = 'block';
    }
    
    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorModal').style.display = 'block';
    }
    
    closeModals() {
        document.getElementById('errorModal').style.display = 'none';
        document.getElementById('imageModal').style.display = 'none';
    }

    closeImageModal() {
        document.getElementById('imageModal').style.display = 'none';
    }
}

// Global functions for modal control
function closeErrorModal() {
    document.getElementById('errorModal').style.display = 'none';
}

function closeImageModal() {
    document.getElementById('imageModal').style.display = 'none';
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.searchDB = new SearchDatabase();
});
