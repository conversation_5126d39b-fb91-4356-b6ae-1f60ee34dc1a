"""
Database Admin - Công cụ quản trị cơ sở dữ liệu
"""

import sys
import os
import shutil
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from flask import Flask
from database.connection import init_database
from database.models import <PERSON>er, Checkin, CustomerHistory

class DatabaseAdmin:
    """Lớp quản trị cơ sở dữ liệu"""
    
    def __init__(self):
        """Khởi tạo admin"""
        self.app = Flask(__name__)
        self.db = init_database(self.app)
    
    def backup_database(self):
        """<PERSON>o lưu cơ sở dữ liệu"""
        try:
            source = 'database/data/ai_gen.db'
            if not os.path.exists(source):
                print("❌ Database không tồn tại!")
                return False
            
            # Tạo tên file backup với timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"ai_gen_backup_{timestamp}.db"
            backup_path = f"database/data/backups/{backup_name}"
            
            # Copy file
            shutil.copy2(source, backup_path)
            print(f"✅ Backup thành công: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi backup: {e}")
            return False
    
    def restore_database(self, backup_file):
        """Khôi phục cơ sở dữ liệu từ backup"""
        try:
            backup_path = f"database/data/backups/{backup_file}"
            if not os.path.exists(backup_path):
                print(f"❌ File backup không tồn tại: {backup_path}")
                return False
            
            # Copy backup về database chính
            shutil.copy2(backup_path, 'database/data/ai_gen.db')
            print(f"✅ Khôi phục thành công từ: {backup_file}")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khôi phục: {e}")
            return False
    
    def show_stats(self):
        """Hiển thị thống kê database"""
        with self.app.app_context():
            try:
                customer_count = Customer.query.count()
                checkin_count = Checkin.query.count()
                history_count = CustomerHistory.query.count()

                print("📊 Thống kê Database:")
                print(f"   👥 Số khách hàng: {customer_count}")
                print(f"   📝 Số lần checkin: {checkin_count}")
                print(f"   📋 Số lịch sử: {history_count}")

            except Exception as e:
                print(f"❌ Lỗi thống kê: {e}")
    
    def cleanup_old_checkins(self, days=7):
        """Dọn dẹp checkin cũ"""
        with self.app.app_context():
            try:
                from datetime import timedelta
                cutoff_date = datetime.utcnow() - timedelta(days=days)

                old_checkins = Checkin.query.filter(Checkin.checkin_time < cutoff_date).all()
                count = len(old_checkins)

                for checkin in old_checkins:
                    self.db.session.delete(checkin)

                self.db.session.commit()
                print(f"✅ Đã xóa {count} checkin cũ hơn {days} ngày")

            except Exception as e:
                print(f"❌ Lỗi dọn dẹp: {e}")
                self.db.session.rollback()

def main():
    """Hàm chính"""
    admin = DatabaseAdmin()
    
    print("🔧 Database Admin Tool")
    print("1. Backup database")
    print("2. Restore database")
    print("3. Hiển thị thống kê")
    print("4. Dọn dẹp checkin cũ")

    choice = input("Chọn chức năng (1-4): ")

    if choice == "1":
        admin.backup_database()
    elif choice == "2":
        backup_file = input("Nhập tên file backup: ")
        admin.restore_database(backup_file)
    elif choice == "3":
        admin.show_stats()
    elif choice == "4":
        days = int(input("Xóa checkin cũ hơn bao nhiêu ngày? (mặc định 7): ") or 7)
        admin.cleanup_old_checkins(days)
    else:
        print("❌ Lựa chọn không hợp lệ!")

if __name__ == "__main__":
    main()
