
from datetime import datetime
from database.connection import db

class Checkin(db.Model):
    __tablename__ = 'TBL_CHECKIN'
    
    checkin_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    customer_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('TBL_CUSTOMER.customer_id'), nullable=True, comment='ID của khách hàng')
    history_id = db.Column(db.Integer, db.<PERSON>ey('TBL_CUSTOMER_HIS.history_id'), nullable=True, comment='ID lịch sử của khách hàng')
    checkin_time = db.Column(db.DateTime, default=datetime.now, comment='Thời gian checkin')
    card_img = db.Column(db.Text, nullable=True, comment='Đường dẫn file ảnh card')
    face_img = db.Column(db.Text, nullable=True, comment='Đường dẫn file ảnh face')
    ai_img = db.Column(db.Text, nullable=True, comment='Đường dẫn file ảnh AI')

    customer = db.relationship('Customer', backref=db.backref('checkins', lazy=True))
    customer_history = db.relationship('CustomerHistory', backref=db.backref('checkins', lazy=True))

    def to_dict(self):
        return {
            'checkin_id': self.checkin_id,
            'customer_id': self.customer_id,
            'history_id': self.history_id,
            'checkin_time': self.checkin_time.isoformat() if self.checkin_time else None,
            'card_img': self.card_img,
            'face_img': self.face_img,
            'ai_img': self.ai_img
        }
    
    def __repr__(self):
        return f'<Checkin {self.checkin_id} - Customer {self.customer_id}>'
