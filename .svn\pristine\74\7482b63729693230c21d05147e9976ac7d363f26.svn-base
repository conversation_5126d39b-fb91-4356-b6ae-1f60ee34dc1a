"""
AI_Gen Application - Main Entry Point
Ứng dụng AI_Gen - Điểm khởi đầu chính

Ứng dụng này sử dụng kiến trúc MVC (Model-View-Controller) với Flask Blueprints
để tạo ra một hệ thống AI tạo ảnh từ thông tin thẻ căn cước công dân.

Chức năng chính:
- Chụp ảnh thẻ căn cước và khuôn mặt từ camera
- Trích xuất thông tin từ thẻ bằng OCR (Optical Character Recognition)
- Tạo ảnh AI dựa trên thông tin đã trích xuất
- Hiển thị kết quả cho người dùng

MVC Architecture with Flask Blueprints
Kiến trúc MVC với Flask Blueprints
"""

# Import các thư viện cần thiết - Import necessary libraries
import os  # Thư viện hệ điều hành để làm việc với biến môi trường và đường dẫn file
import atexit  # Thư viện để đăng ký hàm cleanup khi ứng dụng thoát
from flask import Flask  # Flask framework để tạo web application
from dotenv import load_dotenv  # Thư viện để load biến môi trường từ file .env

# Tải các biến môi trường từ file .env (chứa API keys, cấu hình...)
# Load environment variables from .env file (contains API keys, configurations...)
load_dotenv()

# Import cấu hình ứng dụng - Import application configuration
from config import config, Config

# Import các controller xử lý logic nghiệp vụ - Import controllers handling business logic
from controllers.camera_controller import get_camera_blueprint, camera_controller  # Controller xử lý camera (chụp ảnh, video feed)
from controllers.processing_controller import get_processing_blueprint  # Controller xử lý AI processing (OCR, tạo ảnh)
from controllers.api_controller import get_api_blueprint, get_main_blueprint  # Controller xử lý API và routes chính
from controllers.search_controller import get_search_blueprint  # Controller xử lý tìm kiếm database

# Import các tiện ích hỗ trợ - Import utility functions
from utils.helpers import ensure_directories, logger  # Hàm tạo thư mục và logger để ghi log

# Import database connection - Import kết nối cơ sở dữ liệu
from database.connection import init_database  # Hàm khởi tạo database


def create_app(config_name='default'):
    """
    Application factory pattern - Mẫu thiết kế Factory để tạo ứng dụng

    Hàm này tạo và cấu hình một instance của Flask application.
    Sử dụng Factory Pattern để có thể tạo nhiều instance app khác nhau
    với các cấu hình khác nhau (development, production, testing).

    Args:
        config_name (str): Tên cấu hình muốn sử dụng ('default', 'development', 'production')

    Returns:
        Flask: Instance của Flask application đã được cấu hình
    """

    # Tạo ứng dụng Flask với thư mục template tùy chỉnh
    # Create Flask app with custom template folder
    app = Flask(__name__, template_folder='views')

    # Tải cấu hình cho ứng dụng - Load configuration for the application
    app.config.from_object(config[config_name])  # Áp dụng cấu hình từ config object dựa trên tên
    Config.init_app(app)  # Khởi tạo và áp dụng cấu hình bổ sung cho app

    # Đảm bảo các thư mục cần thiết tồn tại - Ensure required directories exist
    ensure_directories(
        'static/img', 'static/css', 'static/js', 'static/images',  # Thư mục chứa file tĩnh (CSS, JS, hình ảnh)
        'sessions', 'outputs', 'outputs/audio', 'prompts', 'views'  # Thư mục dữ liệu phiên làm việc, kết quả, audio TTS, prompts và templates
    )

    # Đăng ký các blueprint (module con) cho ứng dụng
    # Register blueprints (sub-modules) for the application
    register_blueprints(app)

    # Thiết lập các handler xử lý lỗi - Setup error handlers
    setup_error_handlers(app)

    # Thiết lập cleanup khi ứng dụng thoát - Setup cleanup on exit
    setup_cleanup_handlers()

    # Thêm route kết quả trực tiếp - Add simple result route directly
    @app.route('/result')
    def result():
        """
        Trang kết quả đơn giản với debug chi tiết
        Simple result page with detailed debugging

        Route này hiển thị kết quả cuối cùng của quá trình xử lý:
        - Thông tin thẻ căn cước đã trích xuất
        - Ảnh AI đã được tạo ra
        - Xử lý lỗi nếu không có dữ liệu
        """
        print("🔍 DIRECT RESULT ROUTE ACCESSED")  # Log khi route được truy cập
        from flask import render_template  # Import render_template để render HTML template
        from controllers.camera_controller import camera_controller  # Import camera controller để lấy dữ liệu session

        try:
            # Lấy session hiện tại từ camera controller
            # Get current session from camera controller
            session = camera_controller.session_model.current_session
            print(f"📄 Session exists: {session is not None}")  # Log kiểm tra session có tồn tại không

            if session:  # Nếu có session (có dữ liệu)
                # In thông tin debug về session để kiểm tra dữ liệu
                # Print debug information about session to check data
                print(f"📄 Session ID: {session.get('session_id')}")  # ID phiên làm việc duy nhất
                print(f"📄 Session status: {session.get('status')}")  # Trạng thái phiên (processing, completed, etc.)
                print(f"📄 Card info: {session.get('card_info')}")  # Thông tin thẻ đã trích xuất (tên, ngày sinh, etc.)
                print(f"📄 Card image: {session.get('card_image')}")  # Đường dẫn ảnh thẻ căn cước
                print(f"📄 Face image: {session.get('face_image')}")  # Đường dẫn ảnh khuôn mặt
                print(f"📄 Generated images: {session.get('generated_images')}")  # Danh sách ảnh AI đã tạo

                # Xử lý danh sách ảnh được tạo - CHỈ LẤY 2 ẢNH MỚI NHẤT
                # Process generated images list - ONLY GET LATEST 2 IMAGES
                all_generated_images = []  # Danh sách tất cả ảnh được tạo
                for image_info in session.get('generated_images', []):
                    all_generated_images.append(image_info)  # Thêm từng thông tin ảnh vào danh sách

                # CHỈ LẤY 2 ẢNH MỚI NHẤT để hiển thị (tránh trang quá dài)
                # Only get latest 2 images for display (avoid page being too long)
                generated_images = all_generated_images[-2:]
                print(f"🖼️ Displaying latest 2 images: {generated_images}")

                # Log dữ liệu cuối cùng sẽ được gửi đến template
                # Log final data that will be sent to template
                print(f"📄 Final data - Card info: {session.get('card_info')}")
                print(f"📄 Final data - Generated images: {generated_images}")

                # Render template với dữ liệu thực tế
                # Render template with actual data
                return render_template('combined_file.html',
                                     card_info=session.get('card_info', {}),  # Thông tin thẻ hoặc dict rỗng
                                     generated_images=generated_images)  # Danh sách ảnh đã tạo
            else:
                # Không có session - hiển thị trang kết quả trống
                # No session - show empty result page
                print("📄 No session - showing empty result")
                return render_template('combined_file.html',
                                     card_info={},  # Dict rỗng cho thông tin thẻ
                                     generated_images=[],  # List rỗng cho ảnh
                                     card_image=None,  # Không có ảnh thẻ
                                     face_image=None)  # Không có ảnh khuôn mặt
        except Exception as e:
            # Xử lý lỗi - hiển thị trang kết quả trống và log lỗi
            # Handle errors - show empty result page and log error
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()  # In chi tiết lỗi để debug
            return render_template('combined_file.html',
                                 card_info={},  # Dict rỗng
                                 generated_images=[],  # List rỗng
                                 card_image=None,  # Không có ảnh
                                 face_image=None)  # Không có ảnh

    # Lưu ý: Đã xóa route phục vụ file session vì session không còn lưu file vào đĩa
    # Note: Session files route removed because sessions no longer save files to disk

    # Thêm route test camera để debug - Add test camera route for debugging
    @app.route('/test_camera')
    def test_camera():
        """
        Trang test camera để debug - Test camera page for debugging

        Route này phục vụ file HTML để test camera trực tiếp
        mà không cần qua toàn bộ flow của ứng dụng.
        Hữu ích cho việc debug camera khi có vấn đề.
        """
        try:
            from flask import send_from_directory  # Import để phục vụ file tĩnh từ thư mục
            return send_from_directory('.', 'test_camera.html')  # Trả về file test camera từ thư mục gốc
        except Exception as e:
            print(f"❌ Test camera error: {e}")  # Log lỗi khi không tìm thấy file test camera
            return "Test page not found", 404  # Trả về lỗi 404 nếu file không tồn tại


    # Thêm route test OCR results để debug UI
    @app.route('/test_ocr')
    def test_ocr_page():
        """
        Trang test giao diện OCR results card.
        """
        try:
            from flask import render_template
            return render_template('test_ocr_page.html')
        except Exception as e:
            print(f"❌ Test OCR page error: {e}")
            return "Test page not found", 404

    # Khởi tạo database - Initialize database
    try:
        app.db = init_database(app)  # Khởi tạo database với Flask app
        logger.info("Database initialized successfully")  # Log thành công
    except Exception as e:
        logger.warning(f"Database initialization failed: {e}")  # Log cảnh báo nếu thất bại
        app.db = None  # Đặt None nếu khởi tạo thất bại

    # Khởi tạo Two-Stage AI Pipeline - Initialize Two-Stage AI Pipeline
    try:
        from services.ai_pipeline_service import AIProcessingPipeline  # Import service xử lý AI pipeline
        app.ai_pipeline = AIProcessingPipeline()  # Tạo instance AI pipeline và gán vào app
        logger.info("Two-Stage AI Pipeline initialized successfully")  # Log thông báo khởi tạo thành công
        logger.info("  Stage 1: OCR Processing (Gemini 2.5 Flash)")  # Giai đoạn 1: Trích xuất text từ ảnh
        logger.info("  Stage 2: AI Image Generation (Gemini 2.0 Flash Preview)")  # Giai đoạn 2: Tạo ảnh AI từ text
    except Exception as e:
        logger.warning(f"AI Pipeline initialization failed: {e}")  # Log cảnh báo nếu khởi tạo thất bại
        app.ai_pipeline = None  # Đặt None nếu khởi tạo thất bại (app vẫn chạy được nhưng không có AI)

    logger.info("AI_Gen application created successfully")  # Log thông báo ứng dụng tạo thành công
    return app  # Trả về instance ứng dụng Flask đã được cấu hình đầy đủ\


def register_blueprints(app):
    """
    Đăng ký tất cả blueprint của ứng dụng - Register all application blueprints

    Blueprint là cách Flask tổ chức code thành các module nhỏ.
    Mỗi blueprint chứa các route và logic liên quan đến một chức năng cụ thể.

    Args:
        app (Flask): Instance của Flask application
    """

    # Routes chính (trang chủ, file tĩnh) - Main routes (homepage, static files)
    app.register_blueprint(get_main_blueprint())  # Đăng ký blueprint chính chứa trang chủ và routes cơ bản

    # Thao tác camera (video feed, chụp ảnh) - Camera operations (video feed, capture)
    app.register_blueprint(get_camera_blueprint())  # Đăng ký blueprint camera xử lý video stream và chụp ảnh

    # Thao tác xử lý (OCR, tạo ảnh AI) - Processing operations (OCR, AI generation)
    app.register_blueprint(get_processing_blueprint())  # Đăng ký blueprint xử lý AI và OCR

    # API endpoints - Các endpoint API cho frontend
    app.register_blueprint(get_api_blueprint())  # Đăng ký blueprint API để frontend gọi

    # Search database - Tìm kiếm database
    app.register_blueprint(get_search_blueprint())  # Đăng ký blueprint tìm kiếm database

    logger.info("All blueprints registered successfully")  # Log thông báo đăng ký thành công


def setup_error_handlers(app):
    """
    Thiết lập các handler xử lý lỗi ứng dụng - Setup application error handlers

    Các handler này sẽ bắt và xử lý các lỗi xảy ra trong ứng dụng,
    trả về response phù hợp cho người dùng thay vì crash ứng dụng.

    Args:
        app (Flask): Instance của Flask application
    """

    @app.errorhandler(404)  # Decorator để bắt lỗi HTTP 404 (Not Found)
    def not_found_error(error):
        """
        Xử lý lỗi 404 - trang không tìm thấy
        Handle 404 error - page not found

        Args:
            error: Thông tin lỗi từ Flask

        Returns:
            tuple: (message, status_code)
        """
        logger.warning(f"404 error: {error}")  # Log cảnh báo khi có lỗi 404
        return "Page not found", 404  # Trả về thông báo lỗi 404 cho người dùng

    @app.errorhandler(500)  # Decorator để bắt lỗi HTTP 500 (Internal Server Error)
    def internal_error(error):
        """
        Xử lý lỗi 500 - lỗi server nội bộ
        Handle 500 error - internal server error

        Args:
            error: Thông tin lỗi từ Flask

        Returns:
            tuple: (message, status_code)
        """
        logger.error(f"500 error: {error}")  # Log lỗi nghiêm trọng khi có lỗi 500
        return "Internal server error", 500  # Trả về thông báo lỗi 500 cho người dùng

    @app.errorhandler(Exception)  # Decorator để bắt tất cả exception chưa được xử lý
    def handle_exception(error):
        """
        Xử lý tất cả exception chưa được bắt
        Handle all unhandled exceptions

        Args:
            error: Exception object

        Returns:
            tuple: (message, status_code)
        """
        logger.error(f"Unhandled exception: {error}")  # Log exception chưa được xử lý
        return f"An error occurred: {str(error)}", 500  # Trả về thông báo lỗi chung cho người dùng


def setup_cleanup_handlers():
    """
    Thiết lập các handler cleanup khi ứng dụng tắt - Setup cleanup handlers for application shutdown

    Hàm này đăng ký các hàm cleanup sẽ được gọi tự động khi ứng dụng thoát.
    Đảm bảo tài nguyên được giải phóng đúng cách và dữ liệu cũ được dọn dẹp.
    """

    def cleanup():
        """
        Hàm cleanup được gọi khi ứng dụng thoát - Cleanup function called on application exit

        Thực hiện các tác vụ dọn dẹp:
        1. Giải phóng tài nguyên camera
        2. Xóa session cũ
        3. Xóa file output cũ
        """
        logger.info("Application shutting down...")  # Log thông báo ứng dụng đang tắt

        # Dọn dẹp tài nguyên camera - Cleanup camera resources
        try:
            camera_controller.cleanup()  # Gọi hàm cleanup để giải phóng camera và tài nguyên liên quan
        except Exception as e:
            logger.error(f"Error during camera cleanup: {e}")  # Log lỗi nếu cleanup camera thất bại

        # Dọn dẹp session cũ (cũ hơn 7 ngày) - Cleanup old sessions (older than 7 days)
        try:
            from models.session_model import SessionModel  # Import model quản lý session
            session_model = SessionModel()  # Tạo instance SessionModel
            cleaned_count = session_model.cleanup_old_sessions(days_old=7)  # Xóa session cũ hơn 7 ngày
            if cleaned_count > 0:  # Nếu có session được dọn dẹp
                logger.info(f"Cleaned up {cleaned_count} old sessions")  # Log số lượng session đã xóa
        except Exception as e:
            logger.error(f"Error during session cleanup: {e}")  # Log lỗi nếu cleanup session thất bại

        # Dọn dẹp file output cũ - Cleanup old output files
        try:
            from utils.helpers import cleanup_old_files  # Import hàm tiện ích cleanup file
            cleaned_count = cleanup_old_files('outputs', days_old=30)  # Xóa file trong thư mục outputs cũ hơn 30 ngày
            if cleaned_count > 0:  # Nếu có file được dọn dẹp
                logger.info(f"Cleaned up {cleaned_count} old output files")  # Log số lượng file đã xóa
        except Exception as e:
            logger.error(f"Error during output cleanup: {e}")  # Log lỗi nếu cleanup output thất bại

        logger.info("Application cleanup completed")  # Log thông báo hoàn thành cleanup

    # Đăng ký hàm cleanup với atexit để tự động gọi khi ứng dụng thoát
    # Register cleanup function with atexit to automatically call on application exit
    atexit.register(cleanup)


def print_startup_info():
    """
    In thông tin khởi động ứng dụng (tối giản như project_directory)
    Print application startup information (minimal like project_directory)

    Hiển thị thông tin cơ bản khi ứng dụng khởi động để người dùng biết
    ứng dụng đã sẵn sàng và có thể truy cập qua địa chỉ nào.
    """

# Tạo instance ứng dụng chính - Create main application instance
app = create_app()  # Gọi factory function để tạo và cấu hình ứng dụng Flask

if __name__ == '__main__':
    # Khối code này chỉ chạy khi file được execute trực tiếp (không phải import)
    # This code block only runs when file is executed directly (not imported)

    # In thông tin khởi động - Print startup information
    print_startup_info()  # Gọi hàm hiển thị thông tin khởi động

    # Lấy cấu hình server (BẮT BUỘC debug=False để hoạt động ổn định)
    # Get server configuration (FORCE debug=False for stable operation)
    debug_mode = False  # Bắt buộc tắt debug mode để tránh reload tự động và hoạt động mượt mà
    host = os.environ.get('FLASK_HOST', '0.0.0.0')  # Lấy host từ biến môi trường hoặc dùng mặc định (cho phép truy cập từ mọi IP)
    port = int(os.environ.get('FLASK_PORT', 5000))  # Lấy port từ biến môi trường hoặc dùng mặc định 5000

    logger.info(f"Starting server on {host}:{port} (debug={debug_mode})")  # Log thông tin cấu hình server

    try:
        # Chạy ứng dụng Flask - Run the Flask application
        app.run(
            debug=debug_mode,  # Chế độ debug (False để production)
            host=host,  # Địa chỉ IP host (0.0.0.0 để cho phép truy cập từ mọi IP)
            port=port,  # Cổng port để lắng nghe request
            threaded=True  # Bật chế độ đa luồng để xử lý nhiều request đồng thời
        )
    except KeyboardInterrupt:
        # Xử lý khi người dùng dừng ứng dụng bằng Ctrl+C
        # Handle when user stops application with Ctrl+C
        logger.info("Application stopped by user")  # Log thông báo người dùng dừng ứng dụng
    except Exception as e:
        # Xử lý các lỗi khác khi khởi động ứng dụng
        # Handle other errors during application startup
        logger.error(f"Application startup error: {e}")  # Log lỗi khởi động chi tiết
        raise  # Ném lại exception để hiển thị stack trace đầy đủ


